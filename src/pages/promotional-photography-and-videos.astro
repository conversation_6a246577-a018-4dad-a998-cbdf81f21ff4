---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Promotional Photography and Videos - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Promotional Photography & Videos</h1>
				<p class="hero__subtitle">
					Professional promotional content to showcase your school's unique character and achievements. 
					From prospectus photography to social media content, we help tell your school's story.
				</p>
				<a href="/contact-us" class="hero__cta">Start Your Project</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/promotional-hero.jpg" 
					alt="Professional promotional photography session at school"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Services Overview -->
	<section class="services">
		<div class="services__container">
			<h2 class="services__title">Promotional Content Services</h2>
			<div class="services__grid">
				<div class="service-card">
					<div class="service-card__icon">📸</div>
					<h3 class="service-card__title">Marketing Photography</h3>
					<p class="service-card__description">
						High-quality images for prospectuses, websites, brochures, and advertising materials.
					</p>
					<ul class="service-card__features">
						<li>Lifestyle photography</li>
						<li>Classroom activities</li>
						<li>Facilities showcase</li>
						<li>Student life moments</li>
					</ul>
				</div>
				<div class="service-card">
					<div class="service-card__icon">🎬</div>
					<h3 class="service-card__title">Promotional Videos</h3>
					<p class="service-card__description">
						Professional video content for websites, social media, and recruitment campaigns.
					</p>
					<ul class="service-card__features">
						<li>School overview videos</li>
						<li>Virtual tours</li>
						<li>Student testimonials</li>
						<li>Event highlights</li>
					</ul>
				</div>
				<div class="service-card">
					<div class="service-card__icon">📱</div>
					<h3 class="service-card__title">Social Media Content</h3>
					<p class="service-card__description">
						Engaging content optimized for social media platforms and digital marketing.
					</p>
					<ul class="service-card__features">
						<li>Instagram-ready photos</li>
						<li>Facebook cover images</li>
						<li>Twitter header graphics</li>
						<li>LinkedIn professional shots</li>
					</ul>
				</div>
				<div class="service-card">
					<div class="service-card__icon">🌐</div>
					<h3 class="service-card__title">Website Content</h3>
					<p class="service-card__description">
						Professional imagery and videos specifically designed for your school website.
					</p>
					<ul class="service-card__features">
						<li>Homepage hero images</li>
						<li>Department photography</li>
						<li>Staff professional shots</li>
						<li>Campus lifestyle photos</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Content Types -->
	<section class="content-types">
		<div class="content-types__container">
			<h2 class="content-types__title">Types of Promotional Content</h2>
			<div class="content-types__grid">
				<div class="content-type">
					<div class="content-type__image">
						<img src="/images/lifestyle-photography.jpg" alt="School lifestyle photography" />
					</div>
					<div class="content-type__content">
						<h3 class="content-type__title">Lifestyle Photography</h3>
						<p class="content-type__description">
							Natural, candid shots of students engaged in learning, playing, and interacting. 
							These images capture the authentic spirit and atmosphere of your school.
						</p>
						<ul class="content-type__examples">
							<li>Students in classrooms</li>
							<li>Playground activities</li>
							<li>Library and study areas</li>
							<li>Sports and recreation</li>
							<li>Arts and creative subjects</li>
						</ul>
					</div>
				</div>
				<div class="content-type content-type--reverse">
					<div class="content-type__image">
						<img src="/images/facilities-photography.jpg" alt="School facilities photography" />
					</div>
					<div class="content-type__content">
						<h3 class="content-type__title">Facilities Showcase</h3>
						<p class="content-type__description">
							Professional architectural and interior photography that highlights your school's 
							facilities, infrastructure, and learning environments.
						</p>
						<ul class="content-type__examples">
							<li>Modern classrooms</li>
							<li>Science laboratories</li>
							<li>Sports facilities</li>
							<li>Arts studios</li>
							<li>Library and common areas</li>
						</ul>
					</div>
				</div>
				<div class="content-type">
					<div class="content-type__image">
						<img src="/images/event-photography.jpg" alt="School event photography" />
					</div>
					<div class="content-type__content">
						<h3 class="content-type__title">Event Documentation</h3>
						<p class="content-type__description">
							Comprehensive coverage of school events, celebrations, and special occasions 
							for promotional and archival purposes.
						</p>
						<ul class="content-type__examples">
							<li>Prize giving ceremonies</li>
							<li>Sports day events</li>
							<li>School productions</li>
							<li>Open days</li>
							<li>Special celebrations</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Video Services -->
	<section class="video-services">
		<div class="video-services__container">
			<h2 class="video-services__title">Professional Video Production</h2>
			<div class="video-services__content">
				<div class="video-services__text">
					<h3>Bringing Your School to Life</h3>
					<p>
						Our video production services create compelling visual narratives that showcase your school's 
						unique character, values, and achievements. From promotional videos to virtual tours, 
						we help prospective families experience your school before they visit.
					</p>
					<div class="video-features">
						<div class="video-feature">
							<div class="video-feature__icon">🎯</div>
							<div class="video-feature__content">
								<h4>Strategic Planning</h4>
								<p>We work with you to develop content that aligns with your recruitment and marketing goals.</p>
							</div>
						</div>
						<div class="video-feature">
							<div class="video-feature__icon">🎥</div>
							<div class="video-feature__content">
								<h4>Professional Production</h4>
								<p>High-definition filming with professional equipment and experienced videographers.</p>
							</div>
						</div>
						<div class="video-feature">
							<div class="video-feature__icon">✂️</div>
							<div class="video-feature__content">
								<h4>Expert Editing</h4>
								<p>Professional post-production including editing, color correction, and sound design.</p>
							</div>
						</div>
					</div>
				</div>
				<div class="video-services__image">
					<img 
						src="/images/video-production.jpg" 
						alt="Professional video production at school"
						class="video-services__img"
					/>
				</div>
			</div>
		</div>
	</section>

	<!-- Usage Examples -->
	<section class="usage">
		<div class="usage__container">
			<h2 class="usage__title">How Schools Use Our Content</h2>
			<div class="usage__grid">
				<div class="usage-card">
					<div class="usage-card__icon">📖</div>
					<h3 class="usage-card__title">Prospectus & Brochures</h3>
					<p class="usage-card__description">
						High-quality photography for printed materials that make a lasting impression on prospective families.
					</p>
				</div>
				<div class="usage-card">
					<div class="usage-card__icon">💻</div>
					<h3 class="usage-card__title">Website Content</h3>
					<p class="usage-card__description">
						Professional imagery and videos that enhance your website's visual appeal and engagement.
					</p>
				</div>
				<div class="usage-card">
					<div class="usage-card__icon">📢</div>
					<h3 class="usage-card__title">Social Media Marketing</h3>
					<p class="usage-card__description">
						Engaging content for social media platforms that showcases school life and achievements.
					</p>
				</div>
				<div class="usage-card">
					<div class="usage-card__icon">🏠</div>
					<h3 class="usage-card__title">Open Day Materials</h3>
					<p class="usage-card__description">
						Visual content for presentations, displays, and digital screens during open day events.
					</p>
				</div>
				<div class="usage-card">
					<div class="usage-card__icon">📺</div>
					<h3 class="usage-card__title">Digital Displays</h3>
					<p class="usage-card__description">
						Content for digital notice boards, reception area displays, and interactive presentations.
					</p>
				</div>
				<div class="usage-card">
					<div class="usage-card__icon">📰</div>
					<h3 class="usage-card__title">Press & Media</h3>
					<p class="usage-card__description">
						Professional images for press releases, newspaper articles, and media coverage.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Process -->
	<section class="process">
		<div class="process__container">
			<h2 class="process__title">Our Production Process</h2>
			<div class="process__steps">
				<div class="step">
					<div class="step__number">1</div>
					<div class="step__content">
						<h4>Discovery & Planning</h4>
						<p>We discuss your goals, target audience, and key messages to develop a content strategy.</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">2</div>
					<div class="step__content">
						<h4>Pre-Production</h4>
						<p>Location scouting, scheduling, and coordination with staff and students.</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">3</div>
					<div class="step__content">
						<h4>Production Day</h4>
						<p>Professional photography and videography with minimal disruption to school activities.</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">4</div>
					<div class="step__content">
						<h4>Post-Production</h4>
						<p>Professional editing, color correction, and final delivery in your preferred formats.</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">5</div>
					<div class="step__content">
						<h4>Delivery & Support</h4>
						<p>Final content delivery with ongoing support for implementation and usage.</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Pricing -->
	<section class="pricing">
		<div class="pricing__container">
			<h2 class="pricing__title">Promotional Content Packages</h2>
			<div class="pricing__grid">
				<div class="price-card">
					<h3 class="price-card__title">Essential Package</h3>
					<div class="price-card__price">From £500</div>
					<ul class="price-card__features">
						<li>Half-day photography session</li>
						<li>20-30 edited photographs</li>
						<li>High-resolution files</li>
						<li>Basic usage rights</li>
						<li>Online gallery delivery</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Get Quote</a>
				</div>
				<div class="price-card price-card--featured">
					<div class="price-card__badge">Most Popular</div>
					<h3 class="price-card__title">Complete Package</h3>
					<div class="price-card__price">From £1,200</div>
					<ul class="price-card__features">
						<li>Full-day production</li>
						<li>50+ photographs</li>
						<li>2-3 minute promotional video</li>
						<li>Professional editing</li>
						<li>Multiple format delivery</li>
						<li>Extended usage rights</li>
						<li>Social media optimization</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Book Complete</a>
				</div>
				<div class="price-card">
					<h3 class="price-card__title">Premium Package</h3>
					<div class="price-card__price">From £2,000</div>
					<ul class="price-card__features">
						<li>Multi-day production</li>
						<li>100+ photographs</li>
						<li>Multiple video productions</li>
						<li>Drone/aerial footage</li>
						<li>Professional voice-over</li>
						<li>Brand guidelines compliance</li>
						<li>Ongoing content support</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Enquire Now</a>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Showcase Your School?</h2>
			<p class="cta__description">
				Let us help you create compelling promotional content that attracts new families and celebrates your school community.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Start Your Project</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Services Section */
	.services {
		padding: 4rem 0;
		background: white;
	}

	.services__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.services__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.services__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
	}

	.service-card {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.service-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	}

	.service-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.service-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.service-card__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.service-card__features {
		list-style: none;
		padding: 0;
	}

	.service-card__features li {
		padding: 0.3rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.9rem;
	}

	.service-card__features li::before {
		content: '•';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Content Types */
	.content-types {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.content-types__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.content-types__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.content-types__grid {
		display: flex;
		flex-direction: column;
		gap: 4rem;
	}

	.content-type {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 3rem;
		align-items: center;
	}

	.content-type--reverse {
		direction: rtl;
	}

	.content-type--reverse > * {
		direction: ltr;
	}

	.content-type__image img {
		width: 100%;
		height: 300px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	.content-type__title {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.content-type__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.content-type__examples {
		list-style: none;
		padding: 0;
	}

	.content-type__examples li {
		padding: 0.3rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.9rem;
	}

	.content-type__examples li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Video Services */
	.video-services {
		padding: 4rem 0;
		background: white;
	}

	.video-services__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.video-services__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.video-services__content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 3rem;
		align-items: center;
	}

	.video-services__text h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.video-services__text p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.video-features {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.video-feature {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.video-feature__icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.video-feature__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.video-feature__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
	}

	.video-services__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Usage Section */
	.usage {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.usage__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.usage__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.usage__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.usage-card {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		transition: transform 0.3s ease;
	}

	.usage-card:hover {
		transform: translateY(-4px);
	}

	.usage-card__icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.usage-card__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.usage-card__description {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.95rem;
	}

	/* Process Section */
	.process {
		padding: 4rem 0;
		background: white;
	}

	.process__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.process__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.process__steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
		gap: 2rem;
	}

	.step {
		text-align: center;
		padding: 1.5rem;
	}

	.step__number {
		width: 60px;
		height: 60px;
		background: var(--accent);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.5rem;
		font-weight: 700;
		margin: 0 auto 1rem;
	}

	.step__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.step__content p {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.9rem;
	}

	/* Pricing */
	.pricing {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.pricing__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.pricing__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.pricing__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.price-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		position: relative;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.price-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.price-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.price-card__badge {
		position: absolute;
		top: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--accent);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 1rem;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.price-card__title {
		font-size: 1.4rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.price-card__price {
		font-size: 1.5rem;
		color: var(--accent);
		font-weight: 700;
		margin-bottom: 2rem;
	}

	.price-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.price-card__features li {
		padding: 0.4rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
		font-size: 0.95rem;
	}

	.price-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.price-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.price-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.content-type,
		.video-services__content {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.content-type--reverse {
			direction: ltr;
		}

		.hero__title {
			font-size: 2rem;
		}

		.services__grid,
		.usage__grid {
			grid-template-columns: 1fr;
		}

		.process__steps {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>