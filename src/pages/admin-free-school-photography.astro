---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Admin Free School Photography - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Admin Free School Photography</h1>
				<p class="hero__subtitle">
					Completely eliminate administrative burden from your school office. Our revolutionary admin-free system 
					handles everything from photography to ordering, leaving your staff free to focus on education.
				</p>
				<a href="/contact-us" class="hero__cta">Learn More</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/admin-free-hero.jpg" 
					alt="Admin free school photography process"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- How It Works -->
	<section class="how-it-works">
		<div class="how-it-works__container">
			<h2 class="how-it-works__title">How Our Admin Free System Works</h2>
			<div class="how-it-works__steps">
				<div class="step">
					<div class="step__number">1</div>
					<div class="step__content">
						<h3 class="step__title">Pre-Printed Photo Cards</h3>
						<p class="step__description">
							Our photographers arrive with pre-printed photo cards for each student, eliminating the need for 
							school staff to prepare any paperwork or student lists.
						</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">2</div>
					<div class="step__content">
						<h3 class="step__title">Photography Session</h3>
						<p class="step__description">
							Students are photographed individually or in groups. Each child receives a unique password card 
							that parents will use to access photos online.
						</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">3</div>
					<div class="step__content">
						<h3 class="step__title">Online Preview</h3>
						<p class="step__description">
							Photos are available online within 48 hours. Parents use their unique password to preview 
							their child's photographs on any device.
						</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">4</div>
					<div class="step__content">
						<h3 class="step__title">Direct Ordering</h3>
						<p class="step__description">
							Parents order and pay online using our secure payment gateway. Orders are processed and 
							delivered directly to home addresses.
						</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Benefits for Schools -->
	<section class="benefits">
		<div class="benefits__container">
			<h2 class="benefits__title">Administrative Benefits for Schools</h2>
			<div class="benefits__grid">
				<div class="benefit-card">
					<div class="benefit-card__icon">💰</div>
					<h3 class="benefit-card__title">No Cash Handling</h3>
					<p class="benefit-card__description">
						Eliminate the security risk and administrative burden of handling cash payments in school. 
						All transactions are processed online securely.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">📋</div>
					<h3 class="benefit-card__title">No Proof Card Distribution</h3>
					<p class="benefit-card__description">
						No need for staff to sort and distribute proof cards to hundreds of students. 
						Password cards are given directly during photography.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">🔍</div>
					<h3 class="benefit-card__title">No Photo Matching</h3>
					<p class="benefit-card__description">
						Eliminate the time-consuming task of matching student photos to names and class lists. 
						Our system handles this automatically.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">💸</div>
					<h3 class="benefit-card__title">No Cost to School</h3>
					<p class="benefit-card__description">
						The entire service is provided at no cost to your school. We operate on commission 
						from parent orders only.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">💾</div>
					<h3 class="benefit-card__title">Free Digital Images</h3>
					<p class="benefit-card__description">
						Receive a complete disc of high-resolution digital images for your school's use in 
						prospectuses, websites, and displays.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">🖥️</div>
					<h3 class="benefit-card__title">Free SIMS License</h3>
					<p class="benefit-card__description">
						Where applicable, we provide free SIMS software license for schools that require 
						integration with their management systems.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Parent Experience -->
	<section class="parent-experience">
		<div class="parent-experience__container">
			<div class="parent-experience__content">
				<h2 class="parent-experience__title">Convenient Parent Experience</h2>
				<p class="parent-experience__description">
					Our admin-free system isn't just beneficial for schools - it creates a superior experience 
					for parents too, with modern convenience and flexibility.
				</p>
				<div class="parent-features">
					<div class="parent-feature">
						<div class="parent-feature__icon">📱</div>
						<div class="parent-feature__content">
							<h4>Mobile-Friendly Ordering</h4>
							<p>Parents can view and order photos using smartphones, tablets, or computers at their convenience.</p>
						</div>
					</div>
					<div class="parent-feature">
						<div class="parent-feature__icon">🔒</div>
						<div class="parent-feature__content">
							<h4>Secure Payment Gateway</h4>
							<p>All payments are processed through our secure, encrypted payment system for complete peace of mind.</p>
						</div>
					</div>
					<div class="parent-feature">
						<div class="parent-feature__icon">👨‍👩‍👧‍👦</div>
						<div class="parent-feature__content">
							<h4>Multi-Child Discounts</h4>
							<p>Parents can mix and match images of multiple children in the same family to receive automatic discounts.</p>
						</div>
					</div>
					<div class="parent-feature">
						<div class="parent-feature__icon">🚚</div>
						<div class="parent-feature__content">
							<h4>Direct Home Delivery</h4>
							<p>Orders are professionally packaged and delivered directly to home addresses, eliminating collection hassles.</p>
						</div>
					</div>
				</div>
			</div>
			<div class="parent-experience__image">
				<img 
					src="/images/parent-ordering.jpg" 
					alt="Parent viewing photos online on mobile device"
					class="parent-experience__img"
				/>
			</div>
		</div>
	</section>

	<!-- Comparison -->
	<section class="comparison">
		<div class="comparison__container">
			<h2 class="comparison__title">Traditional vs Admin Free Photography</h2>
			<div class="comparison__table">
				<div class="comparison__header">
					<div class="comparison__column">Traditional Method</div>
					<div class="comparison__column comparison__column--featured">Admin Free System</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Cash Handling:</span>
						<span class="comparison__value comparison__value--negative">❌ Required in school office</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Cash Handling:</span>
						<span class="comparison__value comparison__value--positive">✅ No cash handling needed</span>
					</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Proof Distribution:</span>
						<span class="comparison__value comparison__value--negative">❌ Staff must sort & distribute</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Proof Distribution:</span>
						<span class="comparison__value comparison__value--positive">✅ Direct to parents online</span>
					</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Payment Processing:</span>
						<span class="comparison__value comparison__value--negative">❌ Manual collection & banking</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Payment Processing:</span>
						<span class="comparison__value comparison__value--positive">✅ Automatic online payments</span>
					</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Photo Matching:</span>
						<span class="comparison__value comparison__value--negative">❌ Time-consuming manual task</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Photo Matching:</span>
						<span class="comparison__value comparison__value--positive">✅ Automated system</span>
					</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Order Fulfillment:</span>
						<span class="comparison__value comparison__value--negative">❌ Manual sorting & distribution</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Order Fulfillment:</span>
						<span class="comparison__value comparison__value--positive">✅ Direct home delivery</span>
					</div>
				</div>
				<div class="comparison__row">
					<div class="comparison__item">
						<span class="comparison__label">Administrative Time:</span>
						<span class="comparison__value comparison__value--negative">❌ 10+ hours per session</span>
					</div>
					<div class="comparison__item comparison__item--featured">
						<span class="comparison__label">Administrative Time:</span>
						<span class="comparison__value comparison__value--positive">✅ Zero hours required</span>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Implementation -->
	<section class="implementation">
		<div class="implementation__container">
			<h2 class="implementation__title">Easy Implementation</h2>
			<div class="implementation__content">
				<div class="implementation__text">
					<h3>Getting Started is Simple</h3>
					<p>
						Switching to our admin-free system requires no special preparation or training for your staff. 
						We handle all the technical setup and provide clear communication materials for parents.
					</p>
					<ul class="implementation__benefits">
						<li>No software installation required</li>
						<li>No staff training needed</li>
						<li>Parent information letters provided</li>
						<li>Complete technical support included</li>
						<li>Seamless transition from traditional methods</li>
					</ul>
				</div>
				<div class="implementation__stats">
					<div class="stat-card">
						<div class="stat-card__number">500+</div>
						<div class="stat-card__label">Schools Using Admin Free</div>
					</div>
					<div class="stat-card">
						<div class="stat-card__number">0hrs</div>
						<div class="stat-card__label">Admin Time Required</div>
					</div>
					<div class="stat-card">
						<div class="stat-card__number">48hrs</div>
						<div class="stat-card__label">Photos Available Online</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Testimonials -->
	<section class="testimonials">
		<div class="testimonials__container">
			<h2 class="testimonials__title">What Schools Say About Admin Free</h2>
			<div class="testimonials__grid">
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"The admin-free system has been a game-changer for our school office. We used to spend hours dealing with photo orders, now we don't have to do anything. It's fantastic!"
					</blockquote>
					<cite class="testimonial__author">Linda Hayes, School Secretary - Brookfield Primary</cite>
				</div>
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"Parents love the convenience of ordering online and the quality is excellent. The admin-free approach means our staff can focus on what really matters - the children's education."
					</blockquote>
					<cite class="testimonial__author">Michael Roberts, Head Teacher - Greenway Academy</cite>
				</div>
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"We were skeptical at first, but the admin-free system works perfectly. No more cash handling, no more photo sorting, and parents are much happier with the online ordering."
					</blockquote>
					<cite class="testimonial__author">Sarah Johnson, Office Manager - Meadowbrook High School</cite>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Go Admin Free?</h2>
			<p class="cta__description">
				Join hundreds of schools who have eliminated photography administration forever. 
				Contact us today to learn how easy the transition can be.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Get Started Today</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* How It Works */
	.how-it-works {
		padding: 4rem 0;
		background: white;
	}

	.how-it-works__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.how-it-works__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.how-it-works__steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.step {
		text-align: center;
		padding: 2rem;
	}

	.step__number {
		width: 60px;
		height: 60px;
		background: var(--accent);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.5rem;
		font-weight: 700;
		margin: 0 auto 1.5rem;
	}

	.step__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.step__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Benefits Section */
	.benefits {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.benefits__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.benefits__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.benefits__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.benefit-card {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		transition: transform 0.3s ease;
	}

	.benefit-card:hover {
		transform: translateY(-4px);
	}

	.benefit-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.benefit-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.benefit-card__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Parent Experience */
	.parent-experience {
		padding: 4rem 0;
		background: white;
	}

	.parent-experience__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.parent-experience__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1.5rem;
	}

	.parent-experience__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.parent-features {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.parent-feature {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.parent-feature__icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.parent-feature__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.parent-feature__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
	}

	.parent-experience__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Comparison */
	.comparison {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.comparison__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.comparison__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.comparison__table {
		background: white;
		border-radius: 0.5rem;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.comparison__header {
		display: grid;
		grid-template-columns: 1fr 1fr;
		background: var(--primary);
		color: white;
	}

	.comparison__column {
		padding: 1.5rem;
		text-align: center;
		font-weight: 600;
		font-size: 1.2rem;
	}

	.comparison__column--featured {
		background: var(--accent);
	}

	.comparison__row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		border-bottom: 1px solid #e9ecef;
	}

	.comparison__row:last-child {
		border-bottom: none;
	}

	.comparison__item {
		padding: 1.5rem;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.comparison__item--featured {
		background: rgba(255, 107, 53, 0.05);
	}

	.comparison__label {
		font-weight: 600;
		color: var(--primary);
		font-size: 0.9rem;
	}

	.comparison__value {
		font-size: 0.95rem;
	}

	.comparison__value--positive {
		color: #28a745;
	}

	.comparison__value--negative {
		color: #dc3545;
	}

	/* Implementation */
	.implementation {
		padding: 4rem 0;
		background: white;
	}

	.implementation__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.implementation__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.implementation__content {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.implementation__text h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.implementation__text p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.implementation__benefits {
		list-style: none;
		padding: 0;
	}

	.implementation__benefits li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.implementation__benefits li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.implementation__stats {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.stat-card {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
	}

	.stat-card__number {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--accent);
		margin-bottom: 0.5rem;
	}

	.stat-card__label {
		color: var(--text-muted);
		font-size: 0.9rem;
		font-weight: 500;
	}

	/* Testimonials */
	.testimonials {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.testimonials__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.testimonials__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.testimonials__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.testimonial {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.testimonial__stars {
		color: #ffd700;
		font-size: 1.2rem;
		margin-bottom: 1rem;
	}

	.testimonial__quote {
		font-style: italic;
		color: var(--text-muted);
		margin-bottom: 1rem;
		line-height: 1.6;
	}

	.testimonial__author {
		font-size: 0.9rem;
		color: var(--text-muted);
		font-style: normal;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.parent-experience__container,
		.implementation__content {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.how-it-works__steps {
			grid-template-columns: 1fr;
		}

		.benefits__grid {
			grid-template-columns: 1fr;
		}

		.comparison__header,
		.comparison__row {
			grid-template-columns: 1fr;
		}

		.comparison__column,
		.comparison__item {
			text-align: left;
		}

		.testimonials__grid {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>