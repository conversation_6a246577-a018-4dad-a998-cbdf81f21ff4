---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Portrait Photography - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Portrait Photography</h1>
				<p class="hero__subtitle">
					Professional individual portrait photography for students of all ages. From nursery through to sixth form, 
					we create beautiful, natural portraits that capture each child's unique personality.
				</p>
				<a href="/contact-us" class="hero__cta">Book Portrait Session</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/portrait-hero.jpg" 
					alt="Professional school portrait photography session"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Portrait Types -->
	<section class="portrait-types">
		<div class="portrait-types__container">
			<h2 class="portrait-types__title">Individual Portrait Services</h2>
			<div class="types-grid">
				<div class="type-card">
					<div class="type-card__image">
						<img src="/images/student-headshot.jpg" alt="Student headshot" />
					</div>
					<h3 class="type-card__title">School Headshots</h3>
					<p class="type-card__description">
						Professional headshots for student ID cards, yearbooks, and school records. Clean, consistent styling across all year groups.
					</p>
				</div>
				<div class="type-card">
					<div class="type-card__image">
						<img src="/images/formal-portrait.jpg" alt="Formal student portrait" />
					</div>
					<h3 class="type-card__title">Formal Portraits</h3>
					<p class="type-card__description">
						Traditional formal portraits perfect for special occasions, achievements, and family keepsakes.
					</p>
				</div>
				<div class="type-card">
					<div class="type-card__image">
						<img src="/images/casual-portrait.jpg" alt="Casual student portrait" />
					</div>
					<h3 class="type-card__title">Natural Portraits</h3>
					<p class="type-card__description">
						Relaxed, natural portraits that capture genuine expressions and personality in a comfortable setting.
					</p>
				</div>
				<div class="type-card">
					<div class="type-card__image">
						<img src="/images/staff-portrait.jpg" alt="Staff portrait" />
					</div>
					<h3 class="type-card__title">Staff Portraits</h3>
					<p class="type-card__description">
						Professional portraits for teaching staff, administrators, and support staff for website and school publications.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Photography Approach -->
	<section class="approach">
		<div class="approach__container">
			<div class="approach__content">
				<h2 class="approach__title">Our Portrait Photography Approach</h2>
				<p class="approach__description">
					Every child is unique, and our portrait photography reflects that. We take time to connect with each student, 
					ensuring they feel comfortable and confident during their session.
				</p>
				<div class="approach__points">
					<div class="point">
						<div class="point__icon">😊</div>
						<div class="point__content">
							<h4>Comfortable Environment</h4>
							<p>We create a relaxed atmosphere where students feel at ease and natural.</p>
						</div>
					</div>
					<div class="point">
						<div class="point__icon">⚡</div>
						<div class="point__content">
							<h4>Quick & Efficient</h4>
							<p>Professional sessions that minimize disruption to the school day.</p>
						</div>
					</div>
					<div class="point">
						<div class="point__icon">🎨</div>
						<div class="point__content">
							<h4>Creative Direction</h4>
							<p>Gentle guidance to capture the best expressions and poses.</p>
						</div>
					</div>
					<div class="point">
						<div class="point__icon">📸</div>
						<div class="point__content">
							<h4>High-Quality Results</h4>
							<p>Professional lighting and equipment for consistently excellent portraits.</p>
						</div>
					</div>
				</div>
			</div>
			<div class="approach__image">
				<img 
					src="/images/portrait-session.jpg" 
					alt="Professional portrait photography session in progress"
					class="approach__img"
				/>
			</div>
		</div>
	</section>

	<!-- Age Groups -->
	<section class="age-groups">
		<div class="age-groups__container">
			<h2 class="age-groups__title">Tailored for Every Age Group</h2>
			<div class="age-groups__grid">
				<div class="age-group">
					<div class="age-group__icon">🧒</div>
					<h3 class="age-group__title">Early Years (3-5)</h3>
					<p class="age-group__description">
						Gentle, patient approach with shorter sessions. Fun interactions to capture natural smiles and expressions.
					</p>
					<ul class="age-group__features">
						<li>5-minute sessions</li>
						<li>Gentle guidance</li>
						<li>Bright, cheerful backgrounds</li>
						<li>Parent/teacher support available</li>
					</ul>
				</div>
				<div class="age-group">
					<div class="age-group__icon">👧</div>
					<h3 class="age-group__title">Primary (5-11)</h3>
					<p class="age-group__description">
						Engaging sessions that keep children interested and relaxed. Multiple poses and expressions captured.
					</p>
					<ul class="age-group__features">
						<li>10-minute sessions</li>
						<li>Interactive direction</li>
						<li>Multiple pose options</li>
						<li>Consistent quality across year groups</li>
					</ul>
				</div>
				<div class="age-group">
					<div class="age-group__icon">🧑‍🎓</div>
					<h3 class="age-group__title">Secondary (11-18)</h3>
					<p class="age-group__description">
						Professional approach for teenagers. Confident, mature portraits that students will be proud of.
					</p>
					<ul class="age-group__features">
						<li>15-minute sessions</li>
						<li>Professional guidance</li>
						<li>Variety of poses</li>
						<li>Yearbook-ready quality</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Features & Benefits -->
	<section class="features">
		<div class="features__container">
			<h2 class="features__title">Portrait Photography Features</h2>
			<div class="features__grid">
				<div class="feature-item">
					<div class="feature-item__icon">💫</div>
					<h3 class="feature-item__title">Professional Lighting</h3>
					<p class="feature-item__description">Studio-quality lighting setup for consistent, flattering results.</p>
				</div>
				<div class="feature-item">
					<div class="feature-item__icon">🖼️</div>
					<h3 class="feature-item__title">Multiple Backgrounds</h3>
					<p class="feature-item__description">Choice of backgrounds to suit school branding and preferences.</p>
				</div>
				<div class="feature-item">
					<div class="feature-item__icon">📱</div>
					<h3 class="feature-item__title">Digital Delivery</h3>
					<p class="feature-item__description">High-resolution digital files provided for school and family use.</p>
				</div>
				<div class="feature-item">
					<div class="feature-item__icon">⏱️</div>
					<h3 class="feature-item__title">Fast Turnaround</h3>
					<p class="feature-item__description">Images available online within 48 hours of photography session.</p>
				</div>
				<div class="feature-item">
					<div class="feature-item__icon">🔄</div>
					<h3 class="feature-item__title">Retakes Available</h3>
					<p class="feature-item__description">Complimentary retakes for any unsatisfactory portraits.</p>
				</div>
				<div class="feature-item">
					<div class="feature-item__icon">🎯</div>
					<h3 class="feature-item__title">Consistent Quality</h3>
					<p class="feature-item__description">Standardized approach ensuring uniform quality across all portraits.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Pricing -->
	<section class="pricing">
		<div class="pricing__container">
			<h2 class="pricing__title">Portrait Photography Packages</h2>
			<div class="pricing__note">
				<p>All packages include administration-free service with no cost to the school</p>
			</div>
			<div class="pricing__grid">
				<div class="price-card">
					<h3 class="price-card__title">Basic Portrait Package</h3>
					<div class="price-card__price">Free to School</div>
					<ul class="price-card__features">
						<li>Individual student portraits</li>
						<li>1 pose per student</li>
						<li>Standard background</li>
						<li>Online ordering system</li>
						<li>Digital files for school</li>
						<li>Parent home delivery</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Get Started</a>
				</div>
				<div class="price-card price-card--featured">
					<div class="price-card__badge">Recommended</div>
					<h3 class="price-card__title">Premium Portrait Package</h3>
					<div class="price-card__price">Enhanced Service</div>
					<ul class="price-card__features">
						<li>Everything in Basic Package</li>
						<li>Multiple poses per student</li>
						<li>Choice of backgrounds</li>
						<li>Staff portraits included</li>
						<li>Retake sessions</li>
						<li>High-resolution files</li>
						<li>Yearbook support</li>
						<li>Special event portraits</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Book Premium</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Gallery Preview -->
	<section class="gallery">
		<div class="gallery__container">
			<h2 class="gallery__title">Portrait Photography Examples</h2>
			<div class="gallery__grid">
				<div class="gallery__item">
					<img src="/images/portrait-example-1.jpg" alt="Student portrait example" />
					<div class="gallery__overlay">
						<h4>Primary School Portrait</h4>
						<p>Natural, friendly expression</p>
					</div>
				</div>
				<div class="gallery__item">
					<img src="/images/portrait-example-2.jpg" alt="Student portrait example" />
					<div class="gallery__overlay">
						<h4>Secondary School Portrait</h4>
						<p>Professional, confident style</p>
					</div>
				</div>
				<div class="gallery__item">
					<img src="/images/portrait-example-3.jpg" alt="Student portrait example" />
					<div class="gallery__overlay">
						<h4>Staff Portrait</h4>
						<p>Corporate-quality headshot</p>
					</div>
				</div>
				<div class="gallery__item">
					<img src="/images/portrait-example-4.jpg" alt="Student portrait example" />
					<div class="gallery__overlay">
						<h4>Sixth Form Portrait</h4>
						<p>Formal graduation style</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Book Portrait Photography?</h2>
			<p class="cta__description">
				Contact us today to discuss your school's portrait photography needs and receive a tailored quote.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Get a Quote</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Portrait Types */
	.portrait-types {
		padding: 4rem 0;
		background: white;
	}

	.portrait-types__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.portrait-types__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.types-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
	}

	.type-card {
		background: white;
		border-radius: 0.5rem;
		overflow: hidden;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.type-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	}

	.type-card__image {
		height: 200px;
		overflow: hidden;
	}

	.type-card__image img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.type-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin: 1.5rem 1.5rem 1rem;
	}

	.type-card__description {
		color: var(--text-muted);
		margin: 0 1.5rem 1.5rem;
		line-height: 1.6;
	}

	/* Approach Section */
	.approach {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.approach__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.approach__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1.5rem;
	}

	.approach__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.approach__points {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.point {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.point__icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.point__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.point__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
	}

	.approach__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Age Groups */
	.age-groups {
		padding: 4rem 0;
		background: white;
	}

	.age-groups__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.age-groups__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.age-groups__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.age-group {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
	}

	.age-group__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.age-group__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.age-group__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.age-group__features {
		list-style: none;
		padding: 0;
		text-align: left;
	}

	.age-group__features li {
		padding: 0.3rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.9rem;
	}

	.age-group__features li::before {
		content: '•';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Features */
	.features {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.features__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.features__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.features__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.feature-item {
		text-align: center;
		padding: 1.5rem;
	}

	.feature-item__icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.feature-item__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.feature-item__description {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.95rem;
	}

	/* Pricing */
	.pricing {
		padding: 4rem 0;
		background: white;
	}

	.pricing__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.pricing__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 1rem;
	}

	.pricing__note {
		text-align: center;
		margin-bottom: 3rem;
		padding: 1rem;
		background: #e8f5e8;
		border-radius: 0.5rem;
		color: var(--text-muted);
	}

	.pricing__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.price-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		position: relative;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.price-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.price-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.price-card__badge {
		position: absolute;
		top: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--accent);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 1rem;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.price-card__title {
		font-size: 1.4rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.price-card__price {
		font-size: 1.2rem;
		color: var(--accent);
		font-weight: 600;
		margin-bottom: 2rem;
	}

	.price-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.price-card__features li {
		padding: 0.4rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
		font-size: 0.95rem;
	}

	.price-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.price-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.price-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* Gallery */
	.gallery {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.gallery__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.gallery__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.gallery__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
	}

	.gallery__item {
		position: relative;
		border-radius: 0.5rem;
		overflow: hidden;
		aspect-ratio: 3/4;
		transition: transform 0.3s ease;
	}

	.gallery__item:hover {
		transform: scale(1.05);
	}

	.gallery__item img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.gallery__overlay {
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
		color: white;
		padding: 1.5rem 1rem 1rem;
		transform: translateY(100%);
		transition: transform 0.3s ease;
	}

	.gallery__item:hover .gallery__overlay {
		transform: translateY(0);
	}

	.gallery__overlay h4 {
		margin: 0 0 0.5rem 0;
		font-size: 1.1rem;
	}

	.gallery__overlay p {
		margin: 0;
		font-size: 0.9rem;
		opacity: 0.9;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.approach__container {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.types-grid,
		.age-groups__grid,
		.features__grid {
			grid-template-columns: 1fr;
		}

		.gallery__grid {
			grid-template-columns: repeat(2, 1fr);
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}

	@media (max-width: 480px) {
		.gallery__grid {
			grid-template-columns: 1fr;
		}
	}
</style>