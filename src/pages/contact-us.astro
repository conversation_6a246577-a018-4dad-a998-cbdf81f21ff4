---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Contact Us - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Contact Us</h1>
				<p class="hero__subtitle">
					Ready to book our services or have questions about school photography? 
					Get in touch with our friendly team today. We're here to help make your school photography experience hassle-free.
				</p>
			</div>
		</div>
	</section>

	<!-- Contact Methods -->
	<section class="contact-methods">
		<div class="contact-methods__container">
			<div class="contact-methods__grid">
				<div class="contact-card">
					<div class="contact-card__icon">📞</div>
					<h3 class="contact-card__title">Call Us</h3>
					<p class="contact-card__description">
						Speak directly with our team for immediate assistance and quotes.
					</p>
					<a href="tel:01617235170" class="contact-card__action">0161 723 5170</a>
				</div>
				<div class="contact-card">
					<div class="contact-card__icon">✉️</div>
					<h3 class="contact-card__title">Email Us</h3>
					<p class="contact-card__description">
						Send us your enquiry and we'll respond within 24 hours.
					</p>
					<a href="mailto:<EMAIL>" class="contact-card__action"><EMAIL></a>
				</div>
				<div class="contact-card">
					<div class="contact-card__icon">💼</div>
					<h3 class="contact-card__title">Sales Enquiries</h3>
					<p class="contact-card__description">
						For new bookings and service information.
					</p>
					<a href="mailto:<EMAIL>" class="contact-card__action"><EMAIL></a>
				</div>
			</div>
		</div>
	</section>

	<!-- Contact Form -->
	<section class="contact-form">
		<div class="contact-form__container">
			<div class="contact-form__content">
				<h2 class="contact-form__title">Send Us a Message</h2>
				<p class="contact-form__description">
					Fill out the form below and we'll get back to you as soon as possible. 
					Please include any specific requirements or questions you have about our services.
				</p>
				<form class="form" id="contact-form">
					<div class="form__row">
						<div class="form__group">
							<label for="firstName" class="form__label">First Name *</label>
							<input type="text" id="firstName" name="firstName" class="form__input" required>
						</div>
						<div class="form__group">
							<label for="lastName" class="form__label">Last Name *</label>
							<input type="text" id="lastName" name="lastName" class="form__input" required>
						</div>
					</div>
					<div class="form__row">
						<div class="form__group">
							<label for="email" class="form__label">Email Address *</label>
							<input type="email" id="email" name="email" class="form__input" required>
						</div>
						<div class="form__group">
							<label for="phone" class="form__label">Phone Number</label>
							<input type="tel" id="phone" name="phone" class="form__input">
						</div>
					</div>
					<div class="form__group">
						<label for="schoolName" class="form__label">School Name *</label>
						<input type="text" id="schoolName" name="schoolName" class="form__input" required>
					</div>
					<div class="form__row">
						<div class="form__group">
							<label for="schoolType" class="form__label">School Type</label>
							<select id="schoolType" name="schoolType" class="form__select">
								<option value="">Select school type</option>
								<option value="primary">Primary School</option>
								<option value="secondary">Secondary School</option>
								<option value="academy">Academy</option>
								<option value="independent">Independent School</option>
								<option value="nursery">Nursery</option>
								<option value="other">Other</option>
							</select>
						</div>
						<div class="form__group">
							<label for="studentCount" class="form__label">Number of Students</label>
							<select id="studentCount" name="studentCount" class="form__select">
								<option value="">Select range</option>
								<option value="under-100">Under 100</option>
								<option value="100-300">100-300</option>
								<option value="300-500">300-500</option>
								<option value="500-1000">500-1000</option>
								<option value="over-1000">Over 1000</option>
							</select>
						</div>
					</div>
					<div class="form__group">
						<label for="services" class="form__label">Services Required</label>
						<div class="form__checkboxes">
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="individual-portraits">
								<span class="form__checkbox-text">Individual Portraits</span>
							</label>
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="class-groups">
								<span class="form__checkbox-text">Class Group Photos</span>
							</label>
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="sports-teams">
								<span class="form__checkbox-text">Sports Teams</span>
							</label>
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="promotional">
								<span class="form__checkbox-text">Promotional Photography</span>
							</label>
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="aerial">
								<span class="form__checkbox-text">Aerial/Drone Photography</span>
							</label>
							<label class="form__checkbox">
								<input type="checkbox" name="services" value="virtual-tours">
								<span class="form__checkbox-text">Virtual School Tours</span>
							</label>
						</div>
					</div>
					<div class="form__group">
						<label for="preferredDate" class="form__label">Preferred Date (if known)</label>
						<input type="date" id="preferredDate" name="preferredDate" class="form__input">
					</div>
					<div class="form__group">
						<label for="message" class="form__label">Message</label>
						<textarea id="message" name="message" class="form__textarea" rows="5" placeholder="Please tell us about your requirements, any specific needs, or questions you have..."></textarea>
					</div>
					<button type="submit" class="form__submit">Send Message</button>
				</form>
			</div>
			<div class="contact-form__info">
				<div class="info-card">
					<h3 class="info-card__title">Quick Response</h3>
					<p class="info-card__description">
						We aim to respond to all enquiries within 24 hours during business hours.
					</p>
				</div>
				<div class="info-card">
					<h3 class="info-card__title">Free Consultation</h3>
					<p class="info-card__description">
						All initial consultations and quotes are completely free with no obligation.
					</p>
				</div>
				<div class="info-card">
					<h3 class="info-card__title">Flexible Scheduling</h3>
					<p class="info-card__description">
						We work around your school timetable to minimize disruption.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Office Info -->
	<section class="office-info">
		<div class="office-info__container">
			<div class="office-info__content">
				<h2 class="office-info__title">Our Office</h2>
				<div class="office-info__details">
					<div class="office-detail">
						<div class="office-detail__icon">📍</div>
						<div class="office-detail__content">
							<h4>Address</h4>
							<p>55 Stand Ln, Radcliffe<br>Manchester M26 1LQ</p>
						</div>
					</div>
					<div class="office-detail">
						<div class="office-detail__icon">🕒</div>
						<div class="office-detail__content">
							<h4>Business Hours</h4>
							<p>Monday - Friday: 9:00 AM - 5:30 PM<br>Saturday: 9:00 AM - 1:00 PM<br>Sunday: Closed</p>
						</div>
					</div>
					<div class="office-detail">
						<div class="office-detail__icon">🗺️</div>
						<div class="office-detail__content">
							<h4>Service Area</h4>
							<p>Greater Manchester, Lancashire,<br>Cheshire & surrounding areas</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- FAQ Section -->
	<section class="faq">
		<div class="faq__container">
			<h2 class="faq__title">Frequently Asked Questions</h2>
			<div class="faq__list">
				<details class="faq__item">
					<summary class="faq__question">How far in advance should we book?</summary>
					<div class="faq__answer">
						<p>We recommend booking 4-6 weeks in advance, especially during peak times (September-November and February-April). However, we can often accommodate shorter notice bookings depending on availability.</p>
					</div>
				</details>
				<details class="faq__item">
					<summary class="faq__question">Is there a cost to the school?</summary>
					<div class="faq__answer">
						<p>No, our standard photography services are completely free to schools. We operate on a commission basis from parent orders, so there's no upfront cost or administration burden for your school.</p>
					</div>
				</details>
				<details class="faq__item">
					<summary class="faq__question">How long does a photography session take?</summary>
					<div class="faq__answer">
						<p>This depends on your school size and requirements. Typically, individual portraits take 2-3 minutes per child, and class groups take 10-15 minutes. We'll provide a detailed schedule during planning.</p>
					</div>
				</details>
				<details class="faq__item">
					<summary class="faq__question">What happens if a child is absent on photo day?</summary>
					<div class="faq__answer">
						<p>We offer catch-up sessions for absent children, usually within 2-4 weeks of the main session. This ensures every child has the opportunity to be photographed.</p>
					</div>
				</details>
				<details class="faq__item">
					<summary class="faq__question">Can parents see the photos before ordering?</summary>
					<div class="faq__answer">
						<p>Yes, all photos are available to preview online within 48 hours. Parents receive secure login details to view and order their child's photographs at their convenience.</p>
					</div>
				</details>
			</div>
		</div>
	</section>

	<!-- Call to Action -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Get Started?</h2>
			<p class="cta__description">
				If you would like to find out more and book our services, please get in touch by calling our friendly team.
			</p>
			<div class="cta__actions">
				<a href="tel:01617235170" class="cta__button cta__button--primary">Call 0161 723 5170</a>
				<a href="mailto:<EMAIL>" class="cta__button cta__button--secondary">Email Kerry</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
		text-align: center;
	}

	.hero__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Contact Methods */
	.contact-methods {
		padding: 4rem 0;
		background: white;
	}

	.contact-methods__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.contact-methods__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.contact-card {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.contact-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	}

	.contact-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.contact-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.contact-card__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.contact-card__action {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.contact-card__action:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* Contact Form */
	.contact-form {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.contact-form__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 4rem;
		align-items: start;
	}

	.contact-form__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.contact-form__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.form {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.form__row {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 1rem;
	}

	.form__group {
		margin-bottom: 1.5rem;
	}

	.form__label {
		display: block;
		font-weight: 600;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.form__input,
	.form__select,
	.form__textarea {
		width: 100%;
		padding: 0.75rem;
		border: 2px solid #e9ecef;
		border-radius: 0.5rem;
		font-size: 1rem;
		transition: border-color 0.3s ease;
	}

	.form__input:focus,
	.form__select:focus,
	.form__textarea:focus {
		outline: none;
		border-color: var(--accent);
	}

	.form__checkboxes {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 0.5rem;
		margin-top: 0.5rem;
	}

	.form__checkbox {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		cursor: pointer;
	}

	.form__checkbox input[type="checkbox"] {
		width: auto;
	}

	.form__checkbox-text {
		color: var(--text-muted);
		font-size: 0.95rem;
	}

	.form__submit {
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border: none;
		border-radius: 0.5rem;
		font-size: 1rem;
		font-weight: 600;
		cursor: pointer;
		transition: background-color 0.3s ease;
		width: 100%;
	}

	.form__submit:hover {
		background: var(--accent-hover);
	}

	/* Contact Form Info */
	.contact-form__info {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.info-card {
		background: white;
		padding: 1.5rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.info-card__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.info-card__description {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.95rem;
	}

	/* Office Info */
	.office-info {
		padding: 4rem 0;
		background: white;
	}

	.office-info__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.office-info__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.office-info__details {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.office-detail {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
	}

	.office-detail__icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.office-detail__content h4 {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.office-detail__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
	}

	/* FAQ Section */
	.faq {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.faq__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.faq__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.faq__list {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.faq__item {
		background: white;
		border-radius: 0.5rem;
		overflow: hidden;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	}

	.faq__question {
		padding: 1.5rem;
		cursor: pointer;
		font-weight: 600;
		color: var(--primary);
		list-style: none;
		position: relative;
	}

	.faq__question::-webkit-details-marker {
		display: none;
	}

	.faq__question::after {
		content: '+';
		position: absolute;
		right: 1.5rem;
		font-size: 1.5rem;
		transition: transform 0.3s ease;
	}

	.faq__item[open] .faq__question::after {
		transform: rotate(45deg);
	}

	.faq__answer {
		padding: 0 1.5rem 1.5rem;
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__title {
			font-size: 2rem;
		}

		.contact-methods__grid {
			grid-template-columns: 1fr;
		}

		.contact-form__container {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.form__row {
			grid-template-columns: 1fr;
		}

		.form__checkboxes {
			grid-template-columns: 1fr;
		}

		.office-info__details {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>

<script>
	// Contact form handling
	document.getElementById('contact-form')?.addEventListener('submit', function(e) {
		e.preventDefault();
		
		// Get form data
		const formData = new FormData(this);
		const data = Object.fromEntries(formData.entries());
		
		// Get selected services
		const services = formData.getAll('services');
		data.services = services;
		
		// Here you would normally send the data to your server
		console.log('Form data:', data);
		
		// Show success message (you can customize this)
		alert('Thank you for your message! We will get back to you within 24 hours.');
		
		// Reset form
		this.reset();
	});
</script>