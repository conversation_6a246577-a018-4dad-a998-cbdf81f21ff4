---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Virtual School Tours - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Virtual School Tours</h1>
				<p class="hero__subtitle">
					Showcase your schools to prospective parents with immersive 360° virtual tours. 
					Allow families to explore your facilities from anywhere, providing insights into school life 
					and helping them make informed decisions.
				</p>
				<a href="/contact-us" class="hero__cta">Create Your Virtual Tour</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/virtual-tour-hero.jpg" 
					alt="360 degree virtual tour camera in school"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Benefits Overview -->
	<section class="benefits">
		<div class="benefits__container">
			<h2 class="benefits__title">Why Virtual School Tours Matter</h2>
			<div class="benefits__grid">
				<div class="benefit-card">
					<div class="benefit-card__icon">🏠</div>
					<h3 class="benefit-card__title">24/7 Open Days</h3>
					<p class="benefit-card__description">
						Parents can explore your school at any time that suits them, removing geographical and 
						time constraints from the decision-making process.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">🌍</div>
					<h3 class="benefit-card__title">Reach Wider Audiences</h3>
					<p class="benefit-card__description">
						Attract families from further afield and international students who cannot physically 
						visit before making enrollment decisions.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">💡</div>
					<h3 class="benefit-card__title">Enhanced Website Appeal</h3>
					<p class="benefit-card__description">
						Modern, interactive virtual tours significantly improve your website's engagement and 
						demonstrate your commitment to innovative technology.
					</p>
				</div>
				<div class="benefit-card">
					<div class="benefit-card__icon">📈</div>
					<h3 class="benefit-card__title">Increased Enrollment</h3>
					<p class="benefit-card__description">
						Give prospective families confidence in their choice by allowing them to thoroughly 
						explore your facilities before their first visit.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Tour Features -->
	<section class="features">
		<div class="features__container">
			<h2 class="features__title">Interactive Virtual Tour Features</h2>
			<div class="features__content">
				<div class="features__text">
					<h3>Advanced 360° Technology</h3>
					<p>
						Our virtual tours use cutting-edge 360° photography and interactive technology to create 
						immersive experiences that allow prospective families to explore every corner of your school.
					</p>
					<div class="feature-list">
						<div class="feature-item">
							<div class="feature-item__icon">🔄</div>
							<div class="feature-item__content">
								<h4>Full 360° Views</h4>
								<p>Complete panoramic views of every room and space</p>
							</div>
						</div>
						<div class="feature-item">
							<div class="feature-item__icon">🖱️</div>
							<div class="feature-item__content">
								<h4>Interactive Navigation</h4>
								<p>Click to move between rooms and areas seamlessly</p>
							</div>
						</div>
						<div class="feature-item">
							<div class="feature-item__icon">📱</div>
							<div class="feature-item__content">
								<h4>Mobile Compatible</h4>
								<p>Works perfectly on smartphones, tablets, and computers</p>
							</div>
						</div>
						<div class="feature-item">
							<div class="feature-item__icon">ℹ️</div>
							<div class="feature-item__content">
								<h4>Information Hotspots</h4>
								<p>Interactive points with detailed information about facilities</p>
							</div>
						</div>
					</div>
				</div>
				<div class="features__image">
					<img 
						src="/images/360-camera-setup.jpg" 
						alt="Professional 360 camera equipment setup"
						class="features__img"
					/>
				</div>
			</div>
		</div>
	</section>

	<!-- Areas Covered -->
	<section class="areas">
		<div class="areas__container">
			<h2 class="areas__title">Areas We Can Cover in Your Virtual Tour</h2>
			<div class="areas__grid">
				<div class="area-card">
					<div class="area-card__icon">📚</div>
					<h3 class="area-card__title">Classrooms</h3>
					<p class="area-card__description">
						Primary and secondary classrooms showing learning environments, technology, and resources available to students.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">🔬</div>
					<h3 class="area-card__title">Science Laboratories</h3>
					<p class="area-card__description">
						Specialized science facilities including chemistry, biology, and physics labs with modern equipment.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">💻</div>
					<h3 class="area-card__title">ICT Suites</h3>
					<p class="area-card__description">
						Computer laboratories and technology centers showcasing digital learning capabilities.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">🏃</div>
					<h3 class="area-card__title">Sports Facilities</h3>
					<p class="area-card__description">
						Gymnasiums, sports halls, playing fields, and fitness facilities available for student activities.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">🎭</div>
					<h3 class="area-card__title">Arts & Performance</h3>
					<p class="area-card__description">
						Drama studios, music rooms, art departments, and performance spaces for creative subjects.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">📖</div>
					<h3 class="area-card__title">Library & Learning Resources</h3>
					<p class="area-card__description">
						Library facilities, study areas, and learning resource centers that support student research and study.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">🍽️</div>
					<h3 class="area-card__title">Dining Areas</h3>
					<p class="area-card__description">
						Canteens, dining halls, and kitchen facilities showing meal provision and social spaces.
					</p>
				</div>
				<div class="area-card">
					<div class="area-card__icon">🎯</div>
					<h3 class="area-card__title">Common Areas</h3>
					<p class="area-card__description">
						Reception areas, corridors, assembly halls, and outdoor spaces that define the school environment.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Use Cases -->
	<section class="use-cases">
		<div class="use-cases__container">
			<h2 class="use-cases__title">How Schools Use Virtual Tours</h2>
			<div class="use-cases__content">
				<div class="use-case">
					<div class="use-case__image">
						<img src="/images/prospective-parents.jpg" alt="Parents viewing virtual tour on laptop" />
					</div>
					<div class="use-case__content">
						<h3>Prospective Parent Engagement</h3>
						<p>
							Allow families to explore your school before attending open days, helping them prepare 
							questions and feel more comfortable during their visit. Virtual tours often lead to 
							higher quality conversations during physical visits.
						</p>
						<ul class="use-case__benefits">
							<li>Pre-visit familiarization</li>
							<li>Reduced anxiety for families</li>
							<li>More informed questions</li>
							<li>Better use of open day time</li>
						</ul>
					</div>
				</div>
				<div class="use-case use-case--reverse">
					<div class="use-case__image">
						<img src="/images/academy-conversion.jpg" alt="School leadership team reviewing facilities" />
					</div>
					<div class="use-case__content">
						<h3>Academy Conversion Support</h3>
						<p>
							Schools considering academy status can use virtual tours to showcase their facilities 
							to potential academy sponsors, governors, and stakeholders who may not be able to 
							visit in person regularly.
						</p>
						<ul class="use-case__benefits">
							<li>Remote stakeholder engagement</li>
							<li>Professional facility presentation</li>
							<li>Time-efficient reviews</li>
							<li>Enhanced credibility</li>
						</ul>
					</div>
				</div>
				<div class="use-case">
					<div class="use-case__image">
						<img src="/images/website-integration.jpg" alt="School website with virtual tour embedded" />
					</div>
					<div class="use-case__content">
						<h3>Website Integration</h3>
						<p>
							Embed virtual tours directly into your school website to create an engaging, 
							modern online presence that sets you apart from other schools in your area.
						</p>
						<ul class="use-case__benefits">
							<li>Improved website engagement</li>
							<li>Longer visitor session times</li>
							<li>Reduced bounce rates</li>
							<li>Enhanced SEO benefits</li>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Technology -->
	<section class="technology">
		<div class="technology__container">
			<h2 class="technology__title">Professional Virtual Tour Technology</h2>
			<div class="technology__grid">
				<div class="tech-item">
					<div class="tech-item__icon">📷</div>
					<h3 class="tech-item__title">High-Resolution 360° Cameras</h3>
					<p class="tech-item__description">
						Professional-grade 360° cameras capture ultra-high-resolution imagery that provides 
						crystal clear views of your facilities.
					</p>
				</div>
				<div class="tech-item">
					<div class="tech-item__icon">💾</div>
					<h3 class="tech-item__title">Cloud-Based Hosting</h3>
					<p class="tech-item__description">
						Reliable, fast-loading virtual tours hosted on professional platforms with 99.9% uptime 
						and global content delivery networks.
					</p>
				</div>
				<div class="tech-item">
					<div class="tech-item__icon">🔗</div>
					<h3 class="tech-item__title">Easy Integration</h3>
					<p class="tech-item__description">
						Simple embed codes allow virtual tours to be integrated into any website, social media 
						platform, or digital marketing campaign.
					</p>
				</div>
				<div class="tech-item">
					<div class="tech-item__icon">📊</div>
					<h3 class="tech-item__title">Analytics & Insights</h3>
					<p class="tech-item__description">
						Detailed analytics show which areas of your school are most popular with visitors, 
						helping inform future development decisions.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Process -->
	<section class="process">
		<div class="process__container">
			<h2 class="process__title">Virtual Tour Creation Process</h2>
			<div class="process__steps">
				<div class="step">
					<div class="step__number">1</div>
					<div class="step__content">
						<h4>Consultation & Planning</h4>
						<p>We discuss which areas to include and plan the optimal route through your school</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">2</div>
					<div class="step__content">
						<h4>Professional Photography</h4>
						<p>Our team captures high-resolution 360° imagery of all agreed areas</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">3</div>
					<div class="step__content">
						<h4>Post-Production</h4>
						<p>Images are processed and stitched together to create seamless virtual environments</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">4</div>
					<div class="step__content">
						<h4>Interactive Development</h4>
						<p>Navigation points, information hotspots, and interactive elements are added</p>
					</div>
				</div>
				<div class="step">
					<div class="step__number">5</div>
					<div class="step__content">
						<h4>Testing & Delivery</h4>
						<p>Comprehensive testing across devices before delivery with integration support</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Pricing -->
	<section class="pricing">
		<div class="pricing__container">
			<h2 class="pricing__title">Virtual Tour Packages</h2>
			<div class="pricing__grid">
				<div class="price-card">
					<h3 class="price-card__title">Essential Virtual Tour</h3>
					<div class="price-card__price">From £800</div>
					<ul class="price-card__features">
						<li>5-8 key areas</li>
						<li>Basic navigation</li>
						<li>Mobile compatibility</li>
						<li>Website integration</li>
						<li>12 months hosting</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Get Quote</a>
				</div>
				<div class="price-card price-card--featured">
					<div class="price-card__badge">Most Popular</div>
					<h3 class="price-card__title">Complete Virtual Tour</h3>
					<div class="price-card__price">From £1,500</div>
					<ul class="price-card__features">
						<li>15-20 areas covered</li>
						<li>Interactive hotspots</li>
						<li>Custom branding</li>
						<li>Analytics dashboard</li>
						<li>24 months hosting</li>
						<li>Social media optimization</li>
						<li>Training & support</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Book Now</a>
				</div>
				<div class="price-card">
					<h3 class="price-card__title">Premium Virtual Tour</h3>
					<div class="price-card__price">From £2,500</div>
					<ul class="price-card__features">
						<li>Unlimited areas</li>
						<li>Advanced interactivity</li>
						<li>Virtual reality support</li>
						<li>Multi-language options</li>
						<li>Lifetime hosting</li>
						<li>Annual updates included</li>
						<li>Dedicated account manager</li>
					</ul>
					<a href="/contact-us" class="price-card__cta">Enquire Now</a>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Create Your Virtual School Tour?</h2>
			<p class="cta__description">
				Contact us today to discuss how a virtual tour can enhance your school's online presence 
				and help prospective families explore your excellent facilities.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Start Your Virtual Tour</a>
				<a href="tel:***********" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Benefits Section */
	.benefits {
		padding: 4rem 0;
		background: white;
	}

	.benefits__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.benefits__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.benefits__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
	}

	.benefit-card {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		transition: transform 0.3s ease;
	}

	.benefit-card:hover {
		transform: translateY(-4px);
	}

	.benefit-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.benefit-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.benefit-card__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Features Section */
	.features {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.features__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.features__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.features__content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.features__text h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.features__text p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.feature-list {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.feature-item {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.feature-item__icon {
		font-size: 2rem;
		flex-shrink: 0;
	}

	.feature-item__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.feature-item__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
		font-size: 0.95rem;
	}

	.features__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Areas Section */
	.areas {
		padding: 4rem 0;
		background: white;
	}

	.areas__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.areas__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.areas__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.area-card {
		background: #f8f9fa;
		padding: 1.5rem;
		border-radius: 0.5rem;
		text-align: center;
		transition: transform 0.3s ease;
	}

	.area-card:hover {
		transform: translateY(-4px);
	}

	.area-card__icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.area-card__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.area-card__description {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.95rem;
	}

	/* Use Cases */
	.use-cases {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.use-cases__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.use-cases__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.use-cases__content {
		display: flex;
		flex-direction: column;
		gap: 4rem;
	}

	.use-case {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 3rem;
		align-items: center;
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.use-case--reverse {
		direction: rtl;
	}

	.use-case--reverse > * {
		direction: ltr;
	}

	.use-case__image img {
		width: 100%;
		height: 250px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	.use-case__content h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.use-case__content p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.use-case__benefits {
		list-style: none;
		padding: 0;
	}

	.use-case__benefits li {
		padding: 0.3rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.95rem;
	}

	.use-case__benefits li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Technology */
	.technology {
		padding: 4rem 0;
		background: white;
	}

	.technology__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.technology__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.technology__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.tech-item {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
	}

	.tech-item__icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.tech-item__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.tech-item__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Process */
	.process {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.process__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.process__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.process__steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
		gap: 2rem;
	}

	.step {
		text-align: center;
		padding: 1.5rem;
	}

	.step__number {
		width: 60px;
		height: 60px;
		background: var(--accent);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.5rem;
		font-weight: 700;
		margin: 0 auto 1rem;
	}

	.step__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.step__content p {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.9rem;
	}

	/* Pricing */
	.pricing {
		padding: 4rem 0;
		background: white;
	}

	.pricing__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.pricing__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.pricing__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.price-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		position: relative;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.price-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.price-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.price-card__badge {
		position: absolute;
		top: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--accent);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 1rem;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.price-card__title {
		font-size: 1.4rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.price-card__price {
		font-size: 1.5rem;
		color: var(--accent);
		font-weight: 700;
		margin-bottom: 2rem;
	}

	.price-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.price-card__features li {
		padding: 0.4rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
		font-size: 0.95rem;
	}

	.price-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.price-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.price-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.features__content {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.use-case {
			grid-template-columns: 1fr;
		}

		.use-case--reverse {
			direction: ltr;
		}

		.hero__title {
			font-size: 2rem;
		}

		.benefits__grid,
		.areas__grid,
		.technology__grid {
			grid-template-columns: 1fr;
		}

		.process__steps {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>