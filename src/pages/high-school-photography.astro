---
import Layout from '../layouts/Layout.astro';
---

<Layout title="High School Photography - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">High School Photography</h1>
				<p class="hero__subtitle">
					Professional portrait photography for secondary school students. From Year 7 through to Year 13, 
					we capture confident, mature portraits that students and parents will treasure for years to come.
				</p>
				<a href="/contact-us" class="hero__cta">Book Your Session</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/high-school-hero.jpg" 
					alt="High school students having professional portraits taken"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Services Overview -->
	<section class="services-overview">
		<div class="services-overview__container">
			<h2 class="services-overview__title">Secondary School Photography Services</h2>
			<div class="services-grid">
				<div class="service-item">
					<div class="service-item__icon">🎓</div>
					<h3 class="service-item__title">Year Group Photos</h3>
					<p class="service-item__description">
						Professional year group photography for Years 7-13, including formal portraits and relaxed group shots.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">📚</div>
					<h3 class="service-item__title">Individual Portraits</h3>
					<p class="service-item__description">
						High-quality individual portraits perfect for yearbooks, ID cards, and family keepsakes.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">🏆</div>
					<h3 class="service-item__title">Leavers Photography</h3>
					<p class="service-item__description">
						Special photography sessions for Year 11 and Year 13 leavers, celebrating their achievements.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">⚽</div>
					<h3 class="service-item__title">Sports Teams</h3>
					<p class="service-item__description">
						Team photographs for sports clubs, academic societies, and special interest groups.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Age-Appropriate Approach -->
	<section class="approach">
		<div class="approach__container">
			<div class="approach__content">
				<h2 class="approach__title">Professional Approach for Teenagers</h2>
				<p class="approach__description">
					We understand that photographing teenagers requires a different approach. Our experienced photographers 
					know how to put students at ease and capture natural, confident expressions.
				</p>
				<div class="approach__features">
					<div class="feature">
						<h4 class="feature__title">Relaxed Environment</h4>
						<p class="feature__description">We create a comfortable atmosphere where students feel confident and natural.</p>
					</div>
					<div class="feature">
						<h4 class="feature__title">Quick Sessions</h4>
						<p class="feature__description">Efficient photography that minimizes disruption to lesson time.</p>
					</div>
					<div class="feature">
						<h4 class="feature__title">Professional Results</h4>
						<p class="feature__description">High-quality images suitable for official school records and personal use.</p>
					</div>
				</div>
			</div>
			<div class="approach__image">
				<img 
					src="/images/teenager-portrait-process.jpg" 
					alt="Professional photographer working with high school students"
					class="approach__img"
				/>
			</div>
		</div>
	</section>

	<!-- Leavers Special -->
	<section class="leavers">
		<div class="leavers__container">
			<h2 class="leavers__title">Year 11 & Year 13 Leavers Photography</h2>
			<div class="leavers__grid">
				<div class="leavers__content">
					<h3>Celebrate Their Journey</h3>
					<p>
						Mark this important milestone with professional leavers photography. Our special sessions capture 
						students at this pivotal moment in their educational journey.
					</p>
					<ul class="leavers__features">
						<li>Individual graduation-style portraits</li>
						<li>Group photos with friends</li>
						<li>Academic achievement celebrations</li>
						<li>Formal and casual options available</li>
						<li>High-quality prints and digital copies</li>
					</ul>
				</div>
				<div class="leavers__stats">
					<div class="stat">
						<div class="stat__number">95%</div>
						<div class="stat__label">Student Satisfaction</div>
					</div>
					<div class="stat">
						<div class="stat__number">24hrs</div>
						<div class="stat__label">Online Preview</div>
					</div>
					<div class="stat">
						<div class="stat__number">40+</div>
						<div class="stat__label">Years Experience</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Packages -->
	<section class="packages">
		<div class="packages__container">
			<h2 class="packages__title">High School Photography Packages</h2>
			<div class="packages__grid">
				<div class="package-card">
					<h3 class="package-card__title">Standard Package</h3>
					<div class="package-card__price">No Cost to School</div>
					<ul class="package-card__features">
						<li>Individual portraits (Years 7-13)</li>
						<li>Form group photographs</li>
						<li>Online ordering system</li>
						<li>Parent home delivery</li>
						<li>Digital image disc for school</li>
					</ul>
					<a href="/contact-us" class="package-card__cta">Book Now</a>
				</div>
				<div class="package-card package-card--featured">
					<div class="package-card__badge">Most Popular</div>
					<h3 class="package-card__title">Premium Package</h3>
					<div class="package-card__price">Enhanced Service</div>
					<ul class="package-card__features">
						<li>Everything in Standard</li>
						<li>Year 11 & 13 leavers sessions</li>
						<li>Sports team photography</li>
						<li>Academic society groups</li>
						<li>Retake sessions included</li>
						<li>Special event coverage</li>
						<li>Yearbook support</li>
					</ul>
					<a href="/contact-us" class="package-card__cta">Get Quote</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Process -->
	<section class="process">
		<div class="process__container">
			<h2 class="process__title">Our Simple Process</h2>
			<div class="process__steps">
				<div class="step">
					<div class="step__number">1</div>
					<h4 class="step__title">Consultation</h4>
					<p class="step__description">We discuss your school's specific needs and schedule.</p>
				</div>
				<div class="step">
					<div class="step__number">2</div>
					<h4 class="step__title">Photography Day</h4>
					<p class="step__description">Professional photographers arrive with all equipment ready.</p>
				</div>
				<div class="step">
					<div class="step__number">3</div>
					<h4 class="step__title">Online Preview</h4>
					<p class="step__description">Photos available online within 24-48 hours for review.</p>
				</div>
				<div class="step">
					<div class="step__number">4</div>
					<h4 class="step__title">Ordering & Delivery</h4>
					<p class="step__description">Parents order online, delivery direct to home address.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Testimonials -->
	<section class="testimonials">
		<div class="testimonials__container">
			<h2 class="testimonials__title">What Secondary Schools Say</h2>
			<div class="testimonials__grid">
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"The students loved the professional approach and the photos are excellent quality. Parents have been very positive about the online ordering system."
					</blockquote>
					<cite class="testimonial__author">Rebecca Thompson, Head of Year 11 - Riverside Academy</cite>
				</div>
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"Outstanding service for our Year 13 leavers. The photographers were professional and the students felt comfortable throughout."
					</blockquote>
					<cite class="testimonial__author">David Clark, Deputy Head - Westfield High School</cite>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Book High School Photography?</h2>
			<p class="cta__description">
				Contact us today to discuss your secondary school's photography needs and receive a tailored quote.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Get a Quote</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Services Overview */
	.services-overview {
		padding: 4rem 0;
		background: white;
	}

	.services-overview__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.services-overview__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.services-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.service-item {
		text-align: center;
		padding: 2rem;
		border-radius: 0.5rem;
		background: #f8f9fa;
		transition: transform 0.3s ease;
	}

	.service-item:hover {
		transform: translateY(-4px);
	}

	.service-item__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.service-item__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.service-item__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Approach Section */
	.approach {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.approach__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.approach__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1.5rem;
	}

	.approach__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.approach__features {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.feature__title {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.feature__description {
		color: var(--text-muted);
		line-height: 1.5;
	}

	.approach__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Leavers Section */
	.leavers {
		padding: 4rem 0;
		background: white;
	}

	.leavers__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.leavers__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.leavers__grid {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.leavers__content h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.leavers__content p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.leavers__features {
		list-style: none;
		padding: 0;
	}

	.leavers__features li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.leavers__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.leavers__stats {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.stat {
		text-align: center;
		padding: 1.5rem;
		background: #f8f9fa;
		border-radius: 0.5rem;
	}

	.stat__number {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--accent);
		margin-bottom: 0.5rem;
	}

	.stat__label {
		color: var(--text-muted);
		font-size: 0.9rem;
	}

	/* Packages Section */
	.packages {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.packages__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.packages__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.packages__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.package-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		position: relative;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.package-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.package-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.package-card__badge {
		position: absolute;
		top: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--accent);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 1rem;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.package-card__title {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.package-card__price {
		font-size: 1.2rem;
		color: var(--accent);
		font-weight: 600;
		margin-bottom: 2rem;
	}

	.package-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.package-card__features li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.package-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.package-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.package-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* Process Section */
	.process {
		padding: 4rem 0;
		background: white;
	}

	.process__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.process__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.process__steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 2rem;
	}

	.step {
		text-align: center;
		padding: 2rem 1rem;
	}

	.step__number {
		width: 60px;
		height: 60px;
		background: var(--accent);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 1.5rem;
		font-weight: 700;
		margin: 0 auto 1rem;
	}

	.step__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.step__description {
		color: var(--text-muted);
		line-height: 1.5;
	}

	/* Testimonials */
	.testimonials {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.testimonials__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.testimonials__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.testimonials__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 2rem;
	}

	.testimonial {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.testimonial__stars {
		color: #ffd700;
		font-size: 1.2rem;
		margin-bottom: 1rem;
	}

	.testimonial__quote {
		font-style: italic;
		color: var(--text-muted);
		margin-bottom: 1rem;
		line-height: 1.6;
	}

	.testimonial__author {
		font-size: 0.9rem;
		color: var(--text-muted);
		font-style: normal;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.approach__container {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.leavers__grid {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.leavers__stats {
			flex-direction: row;
			justify-content: space-between;
		}

		.services-grid,
		.process__steps {
			grid-template-columns: 1fr;
		}

		.testimonials__grid {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>