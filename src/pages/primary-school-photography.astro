---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Primary School Photography - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Primary School Photography</h1>
				<p class="hero__subtitle">
					Professional individual and group photography for Key Stage 1 and Key Stage 2 pupils. 
					Our experienced photographers specialize in working with young children to capture natural, beautiful portraits.
				</p>
				<a href="/contact-us" class="hero__cta">Get a Quote</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/primary-school-hero.jpg" 
					alt="Primary school children having their photos taken"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Services Overview -->
	<section class="services-overview">
		<div class="services-overview__container">
			<h2 class="services-overview__title">What We Offer for Primary Schools</h2>
			<div class="services-grid">
				<div class="service-item">
					<div class="service-item__icon">📸</div>
					<h3 class="service-item__title">Individual Portraits</h3>
					<p class="service-item__description">
						Beautiful individual portraits of each child, captured with patience and care to ensure every child feels comfortable.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">👥</div>
					<h3 class="service-item__title">Class Photos</h3>
					<p class="service-item__description">
						Traditional class group photos including teachers, perfect for yearbooks and school memories.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">🎭</div>
					<h3 class="service-item__title">Special Events</h3>
					<p class="service-item__description">
						School plays, sports days, graduation ceremonies, and other special occasions throughout the year.
					</p>
				</div>
				<div class="service-item">
					<div class="service-item__icon">👨‍👩‍👧‍👦</div>
					<h3 class="service-item__title">Sibling Photos</h3>
					<p class="service-item__description">
						Special sibling photographs bringing families together across different year groups.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Admin Free Section -->
	<section class="admin-free">
		<div class="admin-free__container">
			<div class="admin-free__content">
				<h2 class="admin-free__title">Completely Admin Free Service</h2>
				<p class="admin-free__description">
					Our revolutionary admin-free system means zero work for busy school offices. We handle everything from 
					photo cards to online ordering, leaving your staff free to focus on education.
				</p>
				<ul class="admin-free__benefits">
					<li>No cash handling in school</li>
					<li>No proof card distribution</li>
					<li>No matching students to photos</li>
					<li>Free disc of student images</li>
					<li>Photos online within 48 hours</li>
					<li>Direct home delivery for parents</li>
				</ul>
				<a href="/admin-free-school-photography" class="admin-free__link">Learn More About Admin Free</a>
			</div>
			<div class="admin-free__image">
				<img 
					src="/images/admin-free-process.jpg" 
					alt="Admin free photography process"
					class="admin-free__img"
				/>
			</div>
		</div>
	</section>

	<!-- Pricing Section -->
	<section class="pricing">
		<div class="pricing__container">
			<h2 class="pricing__title">Packages for Primary Schools</h2>
			<div class="pricing__grid">
				<div class="pricing-card">
					<h3 class="pricing-card__title">Essential Package</h3>
					<div class="pricing-card__price">Free to School</div>
					<ul class="pricing-card__features">
						<li>Individual portraits</li>
						<li>Class group photos</li>
						<li>Online ordering system</li>
						<li>Free disc of images</li>
						<li>Parent home delivery</li>
					</ul>
					<a href="/contact-us" class="pricing-card__cta">Get Started</a>
				</div>
				<div class="pricing-card pricing-card--featured">
					<h3 class="pricing-card__title">Complete Package</h3>
					<div class="pricing-card__price">Enhanced Service</div>
					<ul class="pricing-card__features">
						<li>Everything in Essential</li>
						<li>Sibling photographs</li>
						<li>Reception welcome photos</li>
						<li>Year 6 leavers photos</li>
						<li>Retake sessions included</li>
						<li>Special event photography</li>
					</ul>
					<a href="/contact-us" class="pricing-card__cta">Book Now</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Testimonials -->
	<section class="testimonials">
		<div class="testimonials__container">
			<h2 class="testimonials__title">What Primary Schools Say</h2>
			<div class="testimonials__grid">
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"The children were so comfortable with the photographer. The photos are beautiful and the admin-free system saved us hours of work."
					</blockquote>
					<cite class="testimonial__author">Sarah Mitchell, Head Teacher - Oakwood Primary</cite>
				</div>
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"Excellent service from start to finish. Parents love the online ordering and the quality is outstanding."
					</blockquote>
					<cite class="testimonial__author">Mark Johnson, Assistant Head - Greenfield Primary</cite>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Book Your Primary School Photography?</h2>
			<p class="cta__description">
				Contact us today for a free consultation and quote tailored to your school's needs.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Get a Quote</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Services Overview */
	.services-overview {
		padding: 4rem 0;
		background: white;
	}

	.services-overview__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.services-overview__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.services-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.service-item {
		text-align: center;
		padding: 2rem;
		border-radius: 0.5rem;
		background: #f8f9fa;
	}

	.service-item__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.service-item__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.service-item__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Admin Free Section */
	.admin-free {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.admin-free__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.admin-free__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1.5rem;
	}

	.admin-free__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.admin-free__benefits {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
	}

	.admin-free__benefits li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.admin-free__benefits li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.admin-free__link {
		color: var(--accent);
		text-decoration: none;
		font-weight: 600;
	}

	.admin-free__img {
		width: 100%;
		height: 300px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Pricing Section */
	.pricing {
		padding: 4rem 0;
		background: white;
	}

	.pricing__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.pricing__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.pricing__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.pricing-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.pricing-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.pricing-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.pricing-card__title {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.pricing-card__price {
		font-size: 1.2rem;
		color: var(--accent);
		font-weight: 600;
		margin-bottom: 2rem;
	}

	.pricing-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.pricing-card__features li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.pricing-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.pricing-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.pricing-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* Testimonials */
	.testimonials {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.testimonials__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.testimonials__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.testimonials__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 2rem;
	}

	.testimonial {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.testimonial__stars {
		color: #ffd700;
		font-size: 1.2rem;
		margin-bottom: 1rem;
	}

	.testimonial__quote {
		font-style: italic;
		color: var(--text-muted);
		margin-bottom: 1rem;
		line-height: 1.6;
	}

	.testimonial__author {
		font-size: 0.9rem;
		color: var(--text-muted);
		font-style: normal;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.admin-free__container {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.services-grid {
			grid-template-columns: 1fr;
		}

		.testimonials__grid {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>