---
import Layout from '../layouts/Layout.astro';
---

<Layout title="School Group Photography - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">School Group Photography</h1>
				<p class="hero__subtitle">
					Professional group photography for classes, sports teams, clubs, and special events. 
					We specialize in organizing and capturing large groups with everyone clearly visible and looking their best.
				</p>
				<a href="/contact-us" class="hero__cta">Book Group Session</a>
			</div>
			<div class="hero__image">
				<img 
					src="/images/group-photography-hero.jpg" 
					alt="School class group photography session"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Group Types -->
	<section class="group-types">
		<div class="group-types__container">
			<h2 class="group-types__title">Types of Group Photography</h2>
			<div class="types-grid">
				<div class="type-card">
					<div class="type-card__icon">📚</div>
					<h3 class="type-card__title">Class Photos</h3>
					<p class="type-card__description">
						Traditional class photographs with all students and teachers clearly visible. Perfect for yearbooks and school records.
					</p>
					<ul class="type-card__features">
						<li>Reception through Year 13</li>
						<li>Teachers included</li>
						<li>Traditional & modern layouts</li>
						<li>High-resolution files</li>
					</ul>
				</div>
				<div class="type-card">
					<div class="type-card__icon">⚽</div>
					<h3 class="type-card__title">Sports Teams</h3>
					<p class="type-card__description">
						Professional team photography for all sports clubs and activities. Action shots and formal team portraits available.
					</p>
					<ul class="type-card__features">
						<li>Football, rugby, netball teams</li>
						<li>Individual & group shots</li>
						<li>Action photography</li>
						<li>Awards ceremonies</li>
					</ul>
				</div>
				<div class="type-card">
					<div class="type-card__icon">🎭</div>
					<h3 class="type-card__title">Clubs & Societies</h3>
					<p class="type-card__description">
						Photography for academic clubs, drama groups, music ensembles, and special interest societies.
					</p>
					<ul class="type-card__features">
						<li>Drama clubs & productions</li>
						<li>Music groups & orchestras</li>
						<li>Academic societies</li>
						<li>Special interest groups</li>
					</ul>
				</div>
				<div class="type-card">
					<div class="type-card__icon">🎓</div>
					<h3 class="type-card__title">Year Group Photos</h3>
					<p class="type-card__description">
						Large format year group photography capturing entire year groups together for special commemorative photos.
					</p>
					<ul class="type-card__features">
						<li>Whole year group photos</li>
						<li>Graduation ceremonies</li>
						<li>Leavers photos</li>
						<li>Special milestones</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Organization Process -->
	<section class="process">
		<div class="process__container">
			<div class="process__content">
				<h2 class="process__title">Our Group Photography Process</h2>
				<p class="process__description">
					Organizing group photography requires careful planning and expertise. Our experienced photographers 
					know how to efficiently arrange groups of any size for the best results.
				</p>
				<div class="process__steps">
					<div class="step">
						<div class="step__number">1</div>
						<div class="step__content">
							<h4>Pre-Planning</h4>
							<p>We work with your staff to schedule sessions and organize groups efficiently.</p>
						</div>
					</div>
					<div class="step">
						<div class="step__number">2</div>
						<div class="step__content">
							<h4>Setup & Arrangement</h4>
							<p>Professional setup with optimal lighting and careful positioning of all individuals.</p>
						</div>
					</div>
					<div class="step">
						<div class="step__number">3</div>
						<div class="step__content">
							<h4>Multiple Shots</h4>
							<p>Several photographs taken to ensure everyone is looking their best in the final image.</p>
						</div>
					</div>
					<div class="step">
						<div class="step__number">4</div>
						<div class="step__content">
							<h4>Quality Review</h4>
							<p>Immediate review to ensure all individuals are clearly visible and well-positioned.</p>
						</div>
					</div>
				</div>
			</div>
			<div class="process__image">
				<img 
					src="/images/group-organization.jpg" 
					alt="Photographer organizing a large school group"
					class="process__img"
				/>
			</div>
		</div>
	</section>

	<!-- Size Specialization -->
	<section class="sizes">
		<div class="sizes__container">
			<h2 class="sizes__title">We Handle Any Group Size</h2>
			<div class="sizes__grid">
				<div class="size-category">
					<div class="size-category__number">5-15</div>
					<h3 class="size-category__title">Small Groups</h3>
					<p class="size-category__description">
						Perfect for clubs, committees, and small teams. Intimate arrangements with individual focus.
					</p>
					<ul class="size-category__examples">
						<li>Student councils</li>
						<li>Small sports teams</li>
						<li>Academic clubs</li>
						<li>Staff departments</li>
					</ul>
				</div>
				<div class="size-category size-category--featured">
					<div class="size-category__number">15-35</div>
					<h3 class="size-category__title">Class Groups</h3>
					<p class="size-category__description">
						Traditional class sizes with optimized arrangements for clarity and professional appearance.
					</p>
					<ul class="size-category__examples">
						<li>Primary classes</li>
						<li>Secondary form groups</li>
						<li>Large sports teams</li>
						<li>Music ensembles</li>
					</ul>
				</div>
				<div class="size-category">
					<div class="size-category__number">35+</div>
					<h3 class="size-category__title">Large Groups</h3>
					<p class="size-category__description">
						Specialized techniques for large year groups and whole school photography.
					</p>
					<ul class="size-category__examples">
						<li>Entire year groups</li>
						<li>Whole school photos</li>
						<li>Large ceremonies</li>
						<li>Special events</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Technical Excellence -->
	<section class="technical">
		<div class="technical__container">
			<h2 class="technical__title">Technical Excellence in Group Photography</h2>
			<div class="technical__grid">
				<div class="technical__item">
					<div class="technical__icon">📸</div>
					<h3 class="technical__name">Professional Equipment</h3>
					<p class="technical__description">High-resolution cameras and specialized group photography lenses for sharp, clear images of every individual.</p>
				</div>
				<div class="technical__item">
					<div class="technical__icon">💡</div>
					<h3 class="technical__name">Optimal Lighting</h3>
					<p class="technical__description">Professional lighting setups to ensure even illumination across the entire group.</p>
				</div>
				<div class="technical__item">
					<div class="technical__icon">📐</div>
					<h3 class="technical__name">Strategic Positioning</h3>
					<p class="technical__description">Expert arrangement techniques to ensure every person is clearly visible and well-positioned.</p>
				</div>
				<div class="technical__item">
					<div class="technical__icon">🎯</div>
					<h3 class="technical__name">Multiple Angles</h3>
					<p class="technical__description">Various shots from different positions to capture the best possible group composition.</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Packages -->
	<section class="packages">
		<div class="packages__container">
			<h2 class="packages__title">Group Photography Packages</h2>
			<div class="packages__grid">
				<div class="package-card">
					<h3 class="package-card__title">Essential Group Package</h3>
					<div class="package-card__price">No Cost to School</div>
					<ul class="package-card__features">
						<li>Class group photographs</li>
						<li>Basic sports teams</li>
						<li>Standard positioning</li>
						<li>Digital files for school</li>
						<li>Online ordering system</li>
						<li>Parent home delivery</li>
					</ul>
					<a href="/contact-us" class="package-card__cta">Get Started</a>
				</div>
				<div class="package-card package-card--featured">
					<div class="package-card__badge">Most Popular</div>
					<h3 class="package-card__title">Complete Group Package</h3>
					<div class="package-card__price">Enhanced Service</div>
					<ul class="package-card__features">
						<li>Everything in Essential</li>
						<li>All sports teams & clubs</li>
						<li>Year group photography</li>
						<li>Special event coverage</li>
						<li>Multiple format options</li>
						<li>Action shots included</li>
						<li>Professional editing</li>
						<li>Yearbook support</li>
					</ul>
					<a href="/contact-us" class="package-card__cta">Book Complete</a>
				</div>
			</div>
		</div>
	</section>

	<!-- Tips for Schools -->
	<section class="tips">
		<div class="tips__container">
			<h2 class="tips__title">Preparing for Group Photography</h2>
			<div class="tips__grid">
				<div class="tip-card">
					<div class="tip-card__icon">👔</div>
					<h3 class="tip-card__title">Dress Code</h3>
					<ul class="tip-card__list">
						<li>School uniform preferred</li>
						<li>Avoid logos or text on clothing</li>
						<li>Neutral colors work best</li>
						<li>Check for uniform consistency</li>
					</ul>
				</div>
				<div class="tip-card">
					<div class="tip-card__icon">⏰</div>
					<h3 class="tip-card__title">Timing</h3>
					<ul class="tip-card__list">
						<li>Morning sessions often work best</li>
						<li>Allow 15-30 minutes per group</li>
						<li>Consider break times</li>
						<li>Weather backup plans</li>
					</ul>
				</div>
				<div class="tip-card">
					<div class="tip-card__icon">📋</div>
					<h3 class="tip-card__title">Organization</h3>
					<ul class="tip-card__list">
						<li>Provide group lists in advance</li>
						<li>Designate group leaders</li>
						<li>Arrange by height if needed</li>
						<li>Have attendance checked</li>
					</ul>
				</div>
			</div>
		</div>
	</section>

	<!-- Testimonials -->
	<section class="testimonials">
		<div class="testimonials__container">
			<h2 class="testimonials__title">What Schools Say About Our Group Photography</h2>
			<div class="testimonials__grid">
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"Fantastic group photography service. The photographer was brilliant with the children and managed to get everyone organized quickly and efficiently."
					</blockquote>
					<cite class="testimonial__author">Emma Richards, Year 3 Teacher - Millfield Primary</cite>
				</div>
				<div class="testimonial">
					<div class="testimonial__stars">★★★★★</div>
					<blockquote class="testimonial__quote">
						"Outstanding results for our sports teams photography. Every player is clearly visible and the quality is excellent for our prospectus."
					</blockquote>
					<cite class="testimonial__author">James Carter, PE Coordinator - Bramley High School</cite>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Ready to Book Group Photography?</h2>
			<p class="cta__description">
				Contact us today to discuss your school's group photography requirements and receive a tailored quote.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Get a Quote</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.hero__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.hero__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Group Types */
	.group-types {
		padding: 4rem 0;
		background: white;
	}

	.group-types__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.group-types__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.types-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
	}

	.type-card {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.type-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
	}

	.type-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.type-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.type-card__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.type-card__features {
		list-style: none;
		padding: 0;
		text-align: left;
	}

	.type-card__features li {
		padding: 0.3rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.9rem;
	}

	.type-card__features li::before {
		content: '•';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Process Section */
	.process {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.process__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.process__title {
		font-size: 2rem;
		color: var(--primary);
		margin-bottom: 1.5rem;
	}

	.process__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.process__steps {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.step {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
	}

	.step__number {
		width: 40px;
		height: 40px;
		background: var(--accent);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 700;
		flex-shrink: 0;
	}

	.step__content h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.step__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
	}

	.process__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Sizes Section */
	.sizes {
		padding: 4rem 0;
		background: white;
	}

	.sizes__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.sizes__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.sizes__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.size-category {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		transition: transform 0.3s ease;
	}

	.size-category:hover {
		transform: translateY(-4px);
	}

	.size-category--featured {
		background: var(--primary);
		color: white;
	}

	.size-category--featured .size-category__title,
	.size-category--featured .size-category__description {
		color: white;
	}

	.size-category__number {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--accent);
		margin-bottom: 1rem;
	}

	.size-category--featured .size-category__number {
		color: var(--accent);
	}

	.size-category__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.size-category__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.size-category__examples {
		list-style: none;
		padding: 0;
		text-align: left;
	}

	.size-category__examples li {
		padding: 0.3rem 0;
		position: relative;
		padding-left: 1.2rem;
		font-size: 0.9rem;
	}

	.size-category__examples li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Technical Section */
	.technical {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.technical__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.technical__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.technical__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.technical__item {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.technical__icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
	}

	.technical__name {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.technical__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Packages */
	.packages {
		padding: 4rem 0;
		background: white;
	}

	.packages__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.packages__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.packages__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
		gap: 2rem;
	}

	.package-card {
		background: white;
		border: 2px solid #e9ecef;
		border-radius: 1rem;
		padding: 2rem;
		text-align: center;
		position: relative;
		transition: transform 0.3s ease, box-shadow 0.3s ease;
	}

	.package-card:hover {
		transform: translateY(-4px);
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.package-card--featured {
		border-color: var(--accent);
		transform: scale(1.05);
	}

	.package-card__badge {
		position: absolute;
		top: -10px;
		left: 50%;
		transform: translateX(-50%);
		background: var(--accent);
		color: white;
		padding: 0.5rem 1rem;
		border-radius: 1rem;
		font-size: 0.8rem;
		font-weight: 600;
	}

	.package-card__title {
		font-size: 1.4rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.package-card__price {
		font-size: 1.2rem;
		color: var(--accent);
		font-weight: 600;
		margin-bottom: 2rem;
	}

	.package-card__features {
		list-style: none;
		padding: 0;
		margin-bottom: 2rem;
		text-align: left;
	}

	.package-card__features li {
		padding: 0.4rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
		font-size: 0.95rem;
	}

	.package-card__features li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.package-card__cta {
		display: inline-block;
		background: var(--accent);
		color: white;
		padding: 0.75rem 1.5rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: background-color 0.3s ease;
	}

	.package-card__cta:hover {
		background: var(--accent-hover);
		color: white;
	}

	/* Tips Section */
	.tips {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.tips__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.tips__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.tips__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
	}

	.tip-card {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.tip-card__icon {
		font-size: 2.5rem;
		text-align: center;
		margin-bottom: 1rem;
	}

	.tip-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 1.5rem;
	}

	.tip-card__list {
		list-style: none;
		padding: 0;
	}

	.tip-card__list li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.tip-card__list li::before {
		content: '•';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	/* Testimonials */
	.testimonials {
		padding: 4rem 0;
		background: white;
	}

	.testimonials__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.testimonials__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.testimonials__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
		gap: 2rem;
	}

	.testimonial {
		background: #f8f9fa;
		padding: 2rem;
		border-radius: 0.5rem;
	}

	.testimonial__stars {
		color: #ffd700;
		font-size: 1.2rem;
		margin-bottom: 1rem;
	}

	.testimonial__quote {
		font-style: italic;
		color: var(--text-muted);
		margin-bottom: 1rem;
		line-height: 1.6;
	}

	.testimonial__author {
		font-size: 0.9rem;
		color: var(--text-muted);
		font-style: normal;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.process__container {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.types-grid,
		.sizes__grid,
		.technical__grid,
		.tips__grid {
			grid-template-columns: 1fr;
		}

		.testimonials__grid {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>