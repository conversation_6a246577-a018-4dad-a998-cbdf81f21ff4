---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Carbon Neutral - JHP Schools">
	<!-- Hero Section -->
	<section class="hero">
		<div class="hero__container">
			<div class="hero__content">
				<h1 class="hero__title">Carbon Neutral Photography</h1>
				<p class="hero__subtitle">
					"Our environmental impact is extremely important to us at JHP as the people we photograph 
					are the ones it will affect the most." We're committed to protecting the environment for future generations.
				</p>
				<div class="hero__badge">
					<img src="/images/carbon-neutral-badge.png" alt="Carbon Neutral Britain Certified" class="hero__badge-img" />
				</div>
			</div>
			<div class="hero__image">
				<img 
					src="/images/environmental-hero.jpg" 
					alt="Environmental sustainability and tree planting"
					class="hero__img"
				/>
			</div>
		</div>
	</section>

	<!-- Our Commitment -->
	<section class="commitment">
		<div class="commitment__container">
			<h2 class="commitment__title">Our Environmental Commitment</h2>
			<div class="commitment__content">
				<div class="commitment__text">
					<p>
						At John Hunt Photography, we recognize that the children we photograph today will inherit 
						the world we leave behind. That's why environmental responsibility isn't just a policy for us 
						- it's a core value that drives everything we do.
					</p>
					<p>
						We have partnered with Carbon Neutral Britain to calculate our carbon footprint and implement 
						meaningful strategies to reduce and offset our environmental impact. Our commitment goes beyond 
						just being carbon neutral - we strive to be carbon negative.
					</p>
					<div class="commitment__achievements">
						<div class="achievement">
							<div class="achievement__icon">🌱</div>
							<div class="achievement__content">
								<h4>Carbon Negative</h4>
								<p>We offset 45 tonnes of carbon annually, exceeding our actual footprint</p>
							</div>
						</div>
						<div class="achievement">
							<div class="achievement__icon">🌳</div>
							<div class="achievement__content">
								<h4>Tree Planting</h4>
								<p>One tree planted for every school we work with - approximately 400 trees annually</p>
							</div>
						</div>
						<div class="achievement">
							<div class="achievement__icon">♻️</div>
							<div class="achievement__content">
								<h4>90% Packaging Reduction</h4>
								<p>Reduced packaging by 90% over 10 years through innovative design and materials</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Sustainability Practices -->
	<section class="practices">
		<div class="practices__container">
			<h2 class="practices__title">Our Sustainability Practices</h2>
			<div class="practices__grid">
				<div class="practice-card">
					<div class="practice-card__icon">📦</div>
					<h3 class="practice-card__title">Reduced Packaging</h3>
					<p class="practice-card__description">
						We've achieved a 90% reduction in packaging over the past 10 years through innovative 
						design and sustainable materials, significantly reducing waste.
					</p>
					<div class="practice-card__impact">
						<span class="impact-stat">90%</span>
						<span class="impact-label">Packaging Reduction</span>
					</div>
				</div>
				<div class="practice-card">
					<div class="practice-card__icon">🔄</div>
					<h3 class="practice-card__title">Reuse & Recycling</h3>
					<p class="practice-card__description">
						All packaging materials are reused wherever possible, and we maintain comprehensive 
						recycling programs throughout our operations.
					</p>
					<div class="practice-card__impact">
						<span class="impact-stat">100%</span>
						<span class="impact-label">Materials Recycled</span>
					</div>
				</div>
				<div class="practice-card">
					<div class="practice-card__icon">⚡</div>
					<h3 class="practice-card__title">Renewable Energy</h3>
					<p class="practice-card__description">
						Our operations are powered by renewable energy sources, reducing our reliance on 
						fossil fuels and minimizing our carbon footprint.
					</p>
					<div class="practice-card__impact">
						<span class="impact-stat">100%</span>
						<span class="impact-label">Renewable Energy</span>
					</div>
				</div>
				<div class="practice-card">
					<div class="practice-card__icon">🧪</div>
					<h3 class="practice-card__title">Chemical Recovery</h3>
					<p class="practice-card__description">
						We clean chemistry in-house using a silver recovery system, preventing harmful 
						chemicals from entering the environment and recovering valuable materials.
					</p>
					<div class="practice-card__impact">
						<span class="impact-stat">100%</span>
						<span class="impact-label">Chemical Recovery</span>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Carbon Offset Projects -->
	<section class="offset-projects">
		<div class="offset-projects__container">
			<h2 class="offset-projects__title">Supporting Environmental Projects</h2>
			<div class="offset-projects__content">
				<div class="offset-projects__text">
					<h3>Where Your School Photography Makes a Difference</h3>
					<p>
						Through our partnership with Carbon Neutral Britain, we contribute to verified environmental 
						projects around the world. These projects not only offset our carbon emissions but create 
						lasting positive impact for communities and ecosystems.
					</p>
					<p>
						We offset 22.17 tonnes of carbon through our initial calculation, but have since expanded 
						our commitment to offset 45 tonnes annually - making us carbon negative and ensuring we 
						contribute more to environmental restoration than we consume.
					</p>
				</div>
				<div class="offset-projects__stats">
					<div class="offset-stat">
						<div class="offset-stat__number">45</div>
						<div class="offset-stat__unit">tonnes</div>
						<div class="offset-stat__label">Carbon Offset Annually</div>
					</div>
					<div class="offset-stat">
						<div class="offset-stat__number">400</div>
						<div class="offset-stat__unit">trees</div>
						<div class="offset-stat__label">Planted Each Year</div>
					</div>
					<div class="offset-stat">
						<div class="offset-stat__number">4</div>
						<div class="offset-stat__unit">standards</div>
						<div class="offset-stat__label">Carbon Certifications</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Certification -->
	<section class="certification">
		<div class="certification__container">
			<h2 class="certification__title">Verified Carbon Neutral Certification</h2>
			<div class="certification__content">
				<div class="certification__info">
					<h3>Independently Verified Standards</h3>
					<p>
						Our carbon neutral status is verified by four major carbon certification standards, 
						ensuring transparency and accountability in our environmental commitments.
					</p>
					<ul class="certification__benefits">
						<li>Independently calculated carbon footprint</li>
						<li>Verified offset project contributions</li>
						<li>Annual monitoring and reporting</li>
						<li>Continuous improvement programs</li>
						<li>Third-party audit compliance</li>
					</ul>
				</div>
				<div class="certification__badges">
					<div class="badge-card">
						<img src="/images/carbon-neutral-britain.png" alt="Carbon Neutral Britain" class="badge-card__img" />
						<h4 class="badge-card__title">Carbon Neutral Britain</h4>
						<p class="badge-card__description">Official partner for carbon footprint calculation and offset projects</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- School Impact -->
	<section class="school-impact">
		<div class="school-impact__container">
			<h2 class="school-impact__title">How Your School Contributes</h2>
			<div class="school-impact__grid">
				<div class="impact-item">
					<div class="impact-item__icon">🌍</div>
					<h3 class="impact-item__title">Global Impact</h3>
					<p class="impact-item__description">
						Every school photography session contributes to our carbon offset projects, 
						supporting reforestation and renewable energy initiatives worldwide.
					</p>
				</div>
				<div class="impact-item">
					<div class="impact-item__icon">🌳</div>
					<h3 class="impact-item__title">Local Tree Planting</h3>
					<p class="impact-item__description">
						Each school we work with results in a tree being planted, creating lasting 
						environmental benefits in communities across the UK.
					</p>
				</div>
				<div class="impact-item">
					<div class="impact-item__icon">📚</div>
					<h3 class="impact-item__title">Educational Value</h3>
					<p class="impact-item__description">
						Schools can use our environmental commitment as part of their sustainability 
						education, showing students real-world environmental responsibility.
					</p>
				</div>
				<div class="impact-item">
					<div class="impact-item__icon">🔮</div>
					<h3 class="impact-item__title">Future Generations</h3>
					<p class="impact-item__description">
						By choosing JHP Schools, you're helping ensure a better environment for the 
						very students we photograph today.
					</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Environmental Education -->
	<section class="education">
		<div class="education__container">
			<h2 class="education__title">Environmental Education Opportunities</h2>
			<div class="education__content">
				<div class="education__text">
					<h3>Teaching Sustainability Through Photography</h3>
					<p>
						We believe in educating the next generation about environmental responsibility. 
						When we visit schools, we're happy to share information about our sustainability 
						practices and how students can make a difference.
					</p>
					<div class="education__activities">
						<div class="activity">
							<h4>Carbon Footprint Awareness</h4>
							<p>Teaching students about carbon footprints and how businesses can be responsible</p>
						</div>
						<div class="activity">
							<h4>Recycling Programs</h4>
							<p>Sharing our packaging reduction and recycling success stories</p>
						</div>
						<div class="activity">
							<h4>Tree Planting Impact</h4>
							<p>Explaining how their school photography session contributes to reforestation</p>
						</div>
					</div>
				</div>
				<div class="education__image">
					<img 
						src="/images/environmental-education.jpg" 
						alt="Students learning about environmental responsibility"
						class="education__img"
					/>
				</div>
			</div>
		</div>
	</section>

	<!-- Future Commitments -->
	<section class="future">
		<div class="future__container">
			<h2 class="future__title">Our Future Environmental Commitments</h2>
			<div class="future__timeline">
				<div class="timeline-item">
					<div class="timeline-item__year">2024</div>
					<div class="timeline-item__content">
						<h4>Carbon Negative Target</h4>
						<p>Maintain our carbon negative status by offsetting 45+ tonnes annually</p>
					</div>
				</div>
				<div class="timeline-item">
					<div class="timeline-item__year">2025</div>
					<div class="timeline-item__content">
						<h4>Zero Waste Goal</h4>
						<p>Achieve zero waste to landfill through comprehensive recycling and reuse programs</p>
					</div>
				</div>
				<div class="timeline-item">
					<div class="timeline-item__year">2026</div>
					<div class="timeline-item__content">
						<h4>Biodiversity Projects</h4>
						<p>Launch local biodiversity projects in partnership with schools and communities</p>
					</div>
				</div>
				<div class="timeline-item">
					<div class="timeline-item__year">2027</div>
					<div class="timeline-item__content">
						<h4>Carbon Neutral Supply Chain</h4>
						<p>Work with suppliers to ensure our entire supply chain achieves carbon neutrality</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- CTA Section -->
	<section class="cta">
		<div class="cta__container">
			<h2 class="cta__title">Choose Environmental Responsibility</h2>
			<p class="cta__description">
				When you choose JHP Schools for your photography needs, you're not just getting exceptional service 
				- you're contributing to a better environment for the students we photograph.
			</p>
			<div class="cta__actions">
				<a href="/contact-us" class="cta__button cta__button--primary">Book Sustainable Photography</a>
				<a href="tel:01617235170" class="cta__button cta__button--secondary">Call 0161 723 5170</a>
			</div>
		</div>
	</section>
</Layout>

<style>
	/* Hero Section */
	.hero {
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		padding: 4rem 0;
	}

	.hero__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.hero__title {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--primary);
		margin-bottom: 1.5rem;
		line-height: 1.2;
	}

	.hero__subtitle {
		font-size: 1.1rem;
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
		font-style: italic;
	}

	.hero__badge {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1rem;
		background: white;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		width: fit-content;
	}

	.hero__badge-img {
		width: 60px;
		height: 60px;
	}

	.hero__img {
		width: 100%;
		height: 400px;
		object-fit: cover;
		border-radius: 1rem;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
	}

	/* Commitment Section */
	.commitment {
		padding: 4rem 0;
		background: white;
	}

	.commitment__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.commitment__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.commitment__text p {
		color: var(--text-muted);
		line-height: 1.7;
		margin-bottom: 2rem;
		font-size: 1.1rem;
	}

	.commitment__achievements {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
		margin-top: 3rem;
	}

	.achievement {
		display: flex;
		gap: 1rem;
		align-items: flex-start;
		padding: 1.5rem;
		background: #f8f9fa;
		border-radius: 0.5rem;
	}

	.achievement__icon {
		font-size: 2.5rem;
		flex-shrink: 0;
	}

	.achievement__content h4 {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.achievement__content p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
		font-size: 0.95rem;
	}

	/* Practices Section */
	.practices {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.practices__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.practices__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.practices__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 2rem;
	}

	.practice-card {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
		transition: transform 0.3s ease;
	}

	.practice-card:hover {
		transform: translateY(-4px);
	}

	.practice-card__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.practice-card__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.practice-card__description {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.practice-card__impact {
		display: flex;
		align-items: baseline;
		gap: 0.5rem;
	}

	.impact-stat {
		font-size: 2rem;
		font-weight: 700;
		color: var(--accent);
	}

	.impact-label {
		color: var(--text-muted);
		font-size: 0.9rem;
	}

	/* Offset Projects */
	.offset-projects {
		padding: 4rem 0;
		background: white;
	}

	.offset-projects__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.offset-projects__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.offset-projects__content {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.offset-projects__text h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.offset-projects__text p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 1.5rem;
	}

	.offset-projects__stats {
		display: flex;
		flex-direction: column;
		gap: 2rem;
	}

	.offset-stat {
		text-align: center;
		padding: 1.5rem;
		background: #f8f9fa;
		border-radius: 0.5rem;
	}

	.offset-stat__number {
		font-size: 2.5rem;
		font-weight: 700;
		color: var(--accent);
		line-height: 1;
	}

	.offset-stat__unit {
		font-size: 1rem;
		color: var(--accent);
		font-weight: 600;
	}

	.offset-stat__label {
		color: var(--text-muted);
		font-size: 0.9rem;
		margin-top: 0.5rem;
	}

	/* Certification */
	.certification {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.certification__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.certification__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.certification__content {
		display: grid;
		grid-template-columns: 2fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.certification__info h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.certification__info p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.certification__benefits {
		list-style: none;
		padding: 0;
	}

	.certification__benefits li {
		padding: 0.5rem 0;
		color: var(--text-muted);
		position: relative;
		padding-left: 1.5rem;
	}

	.certification__benefits li::before {
		content: '✓';
		position: absolute;
		left: 0;
		color: var(--accent);
		font-weight: 600;
	}

	.badge-card {
		background: white;
		padding: 2rem;
		border-radius: 0.5rem;
		text-align: center;
		box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
	}

	.badge-card__img {
		width: 100px;
		height: 100px;
		margin-bottom: 1rem;
	}

	.badge-card__title {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.badge-card__description {
		color: var(--text-muted);
		font-size: 0.9rem;
		line-height: 1.5;
	}

	/* School Impact */
	.school-impact {
		padding: 4rem 0;
		background: white;
	}

	.school-impact__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.school-impact__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.school-impact__grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
	}

	.impact-item {
		text-align: center;
		padding: 2rem;
		background: #f8f9fa;
		border-radius: 0.5rem;
	}

	.impact-item__icon {
		font-size: 3rem;
		margin-bottom: 1rem;
	}

	.impact-item__title {
		font-size: 1.3rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.impact-item__description {
		color: var(--text-muted);
		line-height: 1.6;
	}

	/* Education Section */
	.education {
		padding: 4rem 0;
		background: #f8f9fa;
	}

	.education__container {
		max-width: 1200px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.education__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.education__content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 4rem;
		align-items: center;
	}

	.education__text h3 {
		font-size: 1.5rem;
		color: var(--primary);
		margin-bottom: 1rem;
	}

	.education__text p {
		color: var(--text-muted);
		line-height: 1.6;
		margin-bottom: 2rem;
	}

	.education__activities {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
	}

	.activity h4 {
		font-size: 1.1rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.activity p {
		color: var(--text-muted);
		line-height: 1.5;
		margin: 0;
		font-size: 0.95rem;
	}

	.education__img {
		width: 100%;
		height: 350px;
		object-fit: cover;
		border-radius: 0.5rem;
	}

	/* Future Section */
	.future {
		padding: 4rem 0;
		background: white;
	}

	.future__container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.future__title {
		font-size: 2rem;
		color: var(--primary);
		text-align: center;
		margin-bottom: 3rem;
	}

	.future__timeline {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 2rem;
	}

	.timeline-item {
		text-align: center;
		padding: 2rem 1rem;
		border-radius: 0.5rem;
		background: #f8f9fa;
	}

	.timeline-item__year {
		font-size: 2rem;
		font-weight: 700;
		color: var(--accent);
		margin-bottom: 1rem;
	}

	.timeline-item__content h4 {
		font-size: 1.2rem;
		color: var(--primary);
		margin-bottom: 0.5rem;
	}

	.timeline-item__content p {
		color: var(--text-muted);
		line-height: 1.5;
		font-size: 0.9rem;
	}

	/* CTA Section */
	.cta {
		padding: 4rem 0;
		background: var(--primary);
		color: white;
		text-align: center;
	}

	.cta__container {
		max-width: 800px;
		margin: 0 auto;
		padding: 0 2rem;
	}

	.cta__title {
		font-size: 2rem;
		margin-bottom: 1rem;
	}

	.cta__description {
		font-size: 1.1rem;
		margin-bottom: 2rem;
		opacity: 0.9;
		line-height: 1.6;
	}

	.cta__actions {
		display: flex;
		gap: 1rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.cta__button {
		padding: 1rem 2rem;
		border-radius: 0.5rem;
		text-decoration: none;
		font-weight: 600;
		transition: all 0.3s ease;
	}

	.cta__button--primary {
		background: var(--accent);
		color: white;
	}

	.cta__button--primary:hover {
		background: var(--accent-hover);
		color: white;
	}

	.cta__button--secondary {
		background: transparent;
		color: white;
		border: 2px solid white;
	}

	.cta__button--secondary:hover {
		background: white;
		color: var(--primary);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.hero__container,
		.offset-projects__content,
		.certification__content,
		.education__content {
			grid-template-columns: 1fr;
			gap: 2rem;
		}

		.hero__title {
			font-size: 2rem;
		}

		.commitment__achievements,
		.practices__grid,
		.school-impact__grid {
			grid-template-columns: 1fr;
		}

		.offset-projects__stats {
			flex-direction: row;
			justify-content: space-between;
		}

		.future__timeline {
			grid-template-columns: 1fr;
		}

		.cta__actions {
			flex-direction: column;
			align-items: center;
		}
	}
</style>