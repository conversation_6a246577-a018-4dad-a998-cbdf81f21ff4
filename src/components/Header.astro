---
// Header component for JHP Schools
---

<header class="header header--sticky">
  <div class="header__container">
    <div class="header__logo">
      <a href="/" class="header__logo-link">
        <img 
          src="/images/JHP-Logo-White.png" 
          alt="JHP Schools Logo"
          class="header__logo-image"
          width="200"
          height="60"
        />
      </a>
    </div>

    <nav class="header__nav">
      <button class="header__menu-toggle" type="button" aria-label="Toggle menu" aria-expanded="false">
        <span class="header__menu-icon">
          <span class="header__menu-line"></span>
          <span class="header__menu-line"></span>
          <span class="header__menu-line"></span>
        </span>
      </button>

      <ul class="header__menu" id="main-menu">
        <li class="header__item">
          <a href="/" class="header__link header__link--active">Home</a>
        </li>
        
        <li class="header__item header__item--dropdown">
          <button class="header__link header__dropdown-toggle" aria-expanded="false">
            School Photography Services
            <span class="header__dropdown-arrow"></span>
          </button>
          <ul class="header__dropdown">
            <li><a href="/primary-school-photography" class="header__dropdown-link">Primary School Photography</a></li>
            <li><a href="/high-school-photography" class="header__dropdown-link">High School Photography</a></li>
            <li><a href="/portrait-photography" class="header__dropdown-link">Portrait Photography</a></li>
            <li><a href="/school-group-photography" class="header__dropdown-link">Group Photography</a></li>
            <li><a href="/aerial-photography" class="header__dropdown-link">Drone/Aerial Photography</a></li>
            <li><a href="/360tour" class="header__dropdown-link">Virtual School Tours</a></li>
            <li><a href="/promotional-photography-and-videos" class="header__dropdown-link">Promotional Photos & Videos</a></li>
          </ul>
        </li>

        <li class="header__item header__item--dropdown">
          <a href="/about-us" class="header__link header__dropdown-toggle" aria-expanded="false">
            About Us
            <span class="header__dropdown-arrow"></span>
          </a>
          <ul class="header__dropdown">
            <li><a href="/admin-free-school-photography" class="header__dropdown-link">Admin Free</a></li>
            <li><a href="/carbon-neutral" class="header__dropdown-link">Carbon Neutral</a></li>
          </ul>
        </li>

        <li class="header__item">
          <a href="/contact-us" class="header__link">Contact Us</a>
        </li>

        <li class="header__item">
          <button class="header__cta-button">Get in Touch</button>
        </li>
      </ul>
    </nav>
  </div>
</header>

<style>
  .header {
    background: var(--primary);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1000;
  }

  .header--sticky {
    position: sticky;
    top: 0;
    transition: transform 0.3s ease;
  }

  .header__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .header__logo-link {
    display: block;
  }

  .header__logo-image {
    height: 40px;
    width: auto;
  }

  .header__nav {
    position: relative;
  }

  .header__menu-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
  }

  .header__menu-icon {
    display: block;
    width: 24px;
    height: 18px;
    position: relative;
  }

  .header__menu-line {
    display: block;
    height: 2px;
    width: 100%;
    background: white;
    position: absolute;
    transition: all 0.3s ease;
  }

  .header__menu-line:nth-child(1) { top: 0; }
  .header__menu-line:nth-child(2) { top: 50%; transform: translateY(-50%); }
  .header__menu-line:nth-child(3) { bottom: 0; }

  .header__menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    align-items: center;
    gap: 2rem;
  }

  .header__item {
    position: relative;
  }

  .header__link {
    color: white;
    text-decoration: none;
    padding: 0.5rem 0;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
  }

  .header__link:hover,
  .header__link--active {
    color: var(--accent);
  }

  .header__dropdown-arrow {
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid currentColor;
    transition: transform 0.3s ease;
  }

  .header__dropdown-toggle[aria-expanded="true"] .header__dropdown-arrow {
    transform: rotate(180deg);
  }

  .header__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: var(--primary-dark);
    backdrop-filter: blur(10px);
    border-radius: 0.5rem;
    padding: 1rem;
    min-width: 250px;
    list-style: none;
    margin: 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  }

  .header__item--dropdown:hover .header__dropdown,
  .header__dropdown-toggle[aria-expanded="true"] + .header__dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .header__dropdown-link {
    color: white;
    text-decoration: none;
    padding: 0.75rem 0;
    display: block;
    transition: color 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .header__dropdown-link:hover {
    color: var(--accent);
  }

  .header__dropdown li:last-child .header__dropdown-link {
    border-bottom: none;
  }

  .header__cta-button {
    background: var(--accent);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s ease;
  }

  .header__cta-button:hover {
    background: var(--accent-hover);
  }

  /* Mobile Styles */
  @media (max-width: 768px) {
    .header__container {
      padding: 1rem;
    }

    .header__menu-toggle {
      display: block;
    }

    .header__menu {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: var(--primary);
      backdrop-filter: blur(10px);
      flex-direction: column;
      align-items: stretch;
      gap: 0;
      padding: 1rem;
      opacity: 0;
      visibility: hidden;
      transform: translateY(-20px);
      transition: all 0.3s ease;
    }

    .header__menu--open {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .header__item {
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .header__item:last-child {
      border-bottom: none;
    }

    .header__link {
      padding: 1rem 0;
      justify-content: space-between;
    }

    .header__dropdown {
      position: static;
      opacity: 1;
      visibility: visible;
      transform: none;
      background: rgba(255, 255, 255, 0.1);
      margin-top: 0.5rem;
      max-height: 0;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }

    .header__dropdown-toggle[aria-expanded="true"] + .header__dropdown {
      max-height: 500px;
    }

    .header__cta-button {
      margin-top: 1rem;
      text-align: center;
    }

    /* Mobile menu toggle animation */
    .header__menu-toggle[aria-expanded="true"] .header__menu-line:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }

    .header__menu-toggle[aria-expanded="true"] .header__menu-line:nth-child(2) {
      opacity: 0;
    }

    .header__menu-toggle[aria-expanded="true"] .header__menu-line:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }
  }
</style>

<script>
  // Mobile menu toggle
  const menuToggle = document.querySelector('.header__menu-toggle');
  const menu = document.querySelector('.header__menu');

  if (menuToggle && menu) {
    menuToggle.addEventListener('click', () => {
      const isOpen = menuToggle.getAttribute('aria-expanded') === 'true';
      menuToggle.setAttribute('aria-expanded', (!isOpen).toString());
      menu.classList.toggle('header__menu--open');
    });
  }

  // Dropdown toggles for mobile
  const dropdownToggles = document.querySelectorAll('.header__dropdown-toggle');
  
  dropdownToggles.forEach(toggle => {
    toggle.addEventListener('click', (e) => {
      if (window.innerWidth <= 768) {
        e.preventDefault();
        const isExpanded = toggle.getAttribute('aria-expanded') === 'true';
        toggle.setAttribute('aria-expanded', (!isExpanded).toString());
      }
    });
  });

  // Close mobile menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!e.target.closest('.header__nav')) {
      menu?.classList.remove('header__menu--open');
      menuToggle?.setAttribute('aria-expanded', 'false');
    }
  });

  // Sticky header scroll behavior
  let lastScrollY = window.scrollY;
  const header = document.querySelector('.header--sticky');

  window.addEventListener('scroll', () => {
    const currentScrollY = window.scrollY;
    
    if (header) {
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        header.style.transform = 'translateY(-100%)';
      } else {
        header.style.transform = 'translateY(0)';
      }
    }
    
    lastScrollY = currentScrollY;
  });
</script>