/* Global CSS Variables and Styles */
:root {
  --primary: #10114C;
  --primary-light: #1a1d6e;
  --primary-dark: #0a0c3a;
  --accent: #ff6b35;
  --accent-hover: #e55529;
  --text-light: #ffffff;
  --text-dark: #333333;
  --text-muted: #666666;
  --background: #ffffff;
  --border-light: rgba(255, 255, 255, 0.2);
  --shadow: rgba(0, 0, 0, 0.1);
}

/* Reset and Base Styles */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--background);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 1rem 0;
  font-weight: 600;
  line-height: 1.3;
}

p {
  margin: 0 0 1rem 0;
}

a {
  color: var(--accent);
  text-decoration: none;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-hover);
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.text-center {
  text-align: center;
}

.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}