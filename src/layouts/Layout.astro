---
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import '../styles/global.css';

export interface Props {
	title?: string;
}

const { title = 'JHP Schools - Professional School Photography' } = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<title>{title}</title>
	</head>
	<body>
		<Header />
		<main class="main-content">
			<slot />
		</main>
		<Footer />
	</body>
</html>

<style>
	.main-content {
		min-height: calc(100vh - 200px);
		flex: 1;
	}

	body {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
	}
</style>
