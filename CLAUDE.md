# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

| Command | Purpose |
|---------|---------|
| `npm run dev` | Start development server at localhost:4321 |
| `npm run build` | Build production site to ./dist/ |
| `npm run preview` | Preview build locally before deploying |
| `npm run astro ...` | Run Astro CLI commands (add, check, etc.) |

## Project Overview

This is the **JHP Schools** website - a professional school photography company serving the North West of England for over 40 years. The site showcases their services, admin-free approach, and environmental commitments.

## Architecture Overview

This is an Astro-based static site generator project using TypeScript with strict configuration. The project follows Astro's standard conventions:

- **Pages**: Route-based in `src/pages/` - each `.astro` file becomes a route
- **Components**: Reusable Astro components in `src/components/`
- **Layouts**: Page templates in `src/layouts/` that wrap page content
- **Assets**: Static assets in `src/assets/` and `public/`
- **Styles**: Global CSS variables in `src/styles/global.css`

## Design System

- **Primary Color**: `--primary: #10114C` (dark blue)
- **Accent Color**: `--accent: #ff6b35` (orange)
- **Typography**: System font stack with proper hierarchy
- **Layout**: Responsive grid-based design with mobile-first approach
- **Components**: Header with sticky navigation, Footer with contact info

## Site Structure

### Main Pages:
- `/` - Homepage with hero, services overview, testimonials, FAQ
- `/about-us/` - Company history, values, team, service areas
- `/contact-us/` - Contact form, office details, FAQ

### Service Pages:
- `/primary-school-photography/` - Key Stage 1 & 2 photography
- `/high-school-photography/` - Secondary school and leavers photography
- `/portrait-photography/` - Individual portraits for all ages
- `/school-group-photography/` - Class photos, sports teams, groups
- `/promotional-photography-and-videos/` - Marketing content creation
- `/aerial-photography/` - Drone photography and creative formations
- `/360tour/` - Virtual school tours and 360° photography

### Information Pages:
- `/admin-free-school-photography/` - Detailed USP explanation
- `/carbon-neutral/` - Environmental commitments and practices

## Key Business Information

- **Company**: John Hunt Photography (JHP Schools)
- **Founded**: 1981 (40+ years experience)
- **Service Area**: North West England (Greater Manchester, Lancashire, Cheshire)
- **Contact**: 0161 723 5170, <EMAIL>
- **Address**: 55 Stand Ln, Radcliffe, Manchester M26 1LQ
- **USP**: Admin-free photography service (no cost to schools)
- **Certification**: Carbon Neutral Britain certified

## Content Guidelines

- **Tone**: Professional, family-friendly, trustworthy
- **Key Messages**: 40+ years experience, admin-free service, environmental responsibility
- **Target Audience**: School administrators, head teachers, office managers
- **Call-to-Actions**: Contact forms, phone calls, service bookings
- **Social Proof**: Testimonials from schools, satisfaction statistics

## Technical Notes

- All pages use the main `Layout.astro` component with Header and Footer
- Global CSS variables defined in `src/styles/global.css`
- Contact forms include JavaScript for basic interaction
- Responsive design breakpoints at 768px (tablet) and 480px (mobile)
- Images referenced from `/images/` directory (not yet populated)
- Pages follow consistent structure: Hero > Content Sections > Testimonials > CTA

## Development Patterns

- **Page Structure**: Hero section → Content sections → CTA section
- **Component Naming**: BEM methodology (block__element--modifier)
- **CSS Organization**: Component-scoped styles with global variables
- **Content Hierarchy**: H1 for page titles, H2 for section titles, H3 for subsections
- **Interactive Elements**: Smooth hover transitions, form validation, mobile menu